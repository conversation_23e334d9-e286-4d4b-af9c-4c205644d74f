# 专注数据统计错误修复测试计划

## 🎯 测试目标

验证用户反馈的问题已得到解决：
- **问题**：1小时专注会话+暂停操作 → 记录为2次会话，总时长2小时
- **预期**：1小时专注会话+暂停操作 → 记录为1次会话，总时长1小时

## 📋 测试前准备

### 1. 环境准备
- [ ] 应用已成功构建并运行
- [ ] 测试设备：iPhone模拟器/真机
- [ ] 清理测试数据或使用独立测试科目/项目

### 2. 测试数据准备
- [ ] 创建测试科目："测试科目"
- [ ] 创建测试项目："修复验证项目"
- [ ] 记录测试前的专注记录数量

## 🧪 核心功能测试

### 测试用例1：用户反馈问题重现测试
**目标**：验证原问题已修复

**步骤**：
1. [ ] 启动1小时倒计时专注
2. [ ] 运行15分钟后暂停
3. [ ] 等待2分钟（模拟暂停）
4. [ ] 恢复专注
5. [ ] 继续运行至完成（总计1小时有效专注时间）
6. [ ] 正常结束专注

**预期结果**：
- [ ] 只生成1条专注记录
- [ ] 专注时长约为1小时（58-60分钟，扣除暂停时间）
- [ ] 专注状态为"已完成"
- [ ] 数据统计页面显示正确

**实际结果**：
- 专注记录数量：_____
- 专注时长：_____
- 专注状态：_____
- 数据统计显示：_____

### 测试用例2：多次暂停/恢复测试
**目标**：验证多次暂停不会产生重复记录

**步骤**：
1. [ ] 启动30分钟倒计时专注
2. [ ] 运行10分钟后暂停
3. [ ] 等待1分钟后恢复
4. [ ] 运行10分钟后再次暂停
5. [ ] 等待1分钟后恢复
6. [ ] 继续运行至完成
7. [ ] 正常结束专注

**预期结果**：
- [ ] 只生成1条专注记录
- [ ] 专注时长约为30分钟（扣除2分钟暂停时间）
- [ ] 专注状态为"已完成"

**实际结果**：
- 专注记录数量：_____
- 专注时长：_____
- 专注状态：_____

### 测试用例3：正计时模式测试
**目标**：验证正计时模式的修复效果

**步骤**：
1. [ ] 启动正计时专注
2. [ ] 运行20分钟后暂停
3. [ ] 等待2分钟后恢复
4. [ ] 继续运行20分钟
5. [ ] 手动结束专注

**预期结果**：
- [ ] 只生成1条专注记录
- [ ] 专注时长约为40分钟（扣除暂停时间）
- [ ] 专注状态为"已完成"

**实际结果**：
- 专注记录数量：_____
- 专注时长：_____
- 专注状态：_____

### 测试用例4：60秒内结束测试
**目标**：验证短时间专注不会被记录

**步骤**：
1. [ ] 启动5分钟倒计时专注
2. [ ] 运行30秒后手动结束
3. [ ] 确认退出

**预期结果**：
- [ ] 不生成专注记录
- [ ] 数据统计不变

**实际结果**：
- 专注记录数量变化：_____
- 数据统计变化：_____

## 🔍 边界情况测试

### 测试用例5：应用后台/前台切换
**目标**：验证后台切换不影响数据记录

**步骤**：
1. [ ] 启动30分钟倒计时专注
2. [ ] 运行10分钟后切换到后台
3. [ ] 等待2分钟后切回前台
4. [ ] 继续运行至完成

**预期结果**：
- [ ] 只生成1条专注记录
- [ ] 时长计算正确

**实际结果**：
- 专注记录数量：_____
- 专注时长：_____

### 测试用例6：强制退出应用测试
**目标**：验证应用被杀死后的数据处理

**步骤**：
1. [ ] 启动30分钟倒计时专注
2. [ ] 运行15分钟后强制关闭应用
3. [ ] 重新打开应用
4. [ ] 检查专注记录

**预期结果**：
- [ ] 生成1条中断状态的专注记录
- [ ] 时长约为15分钟

**实际结果**：
- 专注记录数量：_____
- 专注时长：_____
- 专注状态：_____

## 📊 数据验证测试

### 测试用例7：数据统计页面验证
**目标**：验证数据统计显示正确

**步骤**：
1. [ ] 完成上述测试用例1-3
2. [ ] 进入数据分析页面
3. [ ] 检查今日专注数据
4. [ ] 检查专注记录列表

**预期结果**：
- [ ] 今日专注次数正确
- [ ] 今日专注时长正确
- [ ] 专注记录列表显示正确

**实际结果**：
- 今日专注次数：_____
- 今日专注时长：_____
- 记录列表条数：_____

### 测试用例8：数据修复工具测试
**目标**：验证数据修复工具功能

**步骤**：
1. [ ] 打开数据修复工具页面
2. [ ] 点击"重新检测"
3. [ ] 查看检测报告
4. [ ] 如有重复数据，执行修复

**预期结果**：
- [ ] 工具正常运行
- [ ] 检测报告准确
- [ ] 修复功能正常

**实际结果**：
- 检测到重复记录：_____条
- 修复结果：_____

## 🚨 回归测试

### 测试用例9：其他功能验证
**目标**：确保修复没有影响其他功能

**测试项目**：
- [ ] 科目/项目管理功能正常
- [ ] 数据分析其他页面正常
- [ ] 设置页面功能正常
- [ ] 应用整体性能正常

## 📝 测试记录

### 测试环境
- 测试日期：_____
- 测试设备：_____
- 应用版本：_____
- 测试人员：_____

### 测试结果汇总
- [ ] ✅ 用户反馈问题已修复
- [ ] ✅ 防重复保存机制有效
- [ ] ✅ 时长计算准确
- [ ] ✅ 数据统计正确
- [ ] ✅ 无新增问题
- [ ] ✅ 回归测试通过

### 发现的问题
1. 问题描述：_____
   - 严重程度：_____
   - 影响范围：_____
   - 修复建议：_____

2. 问题描述：_____
   - 严重程度：_____
   - 影响范围：_____
   - 修复建议：_____

### 测试结论
- [ ] ✅ 修复成功，可以发布
- [ ] ❌ 存在问题，需要进一步修复
- [ ] ⚠️ 部分问题，建议优化

### 备注
_____

## 📋 测试检查清单

在提交代码前，确保以下所有项目都已完成：

- [ ] 所有核心测试用例已执行
- [ ] 用户反馈问题已验证修复
- [ ] 数据统计显示正确
- [ ] 无新增严重问题
- [ ] 回归测试通过
- [ ] 测试记录已完整填写
- [ ] 代码已通过静态分析
- [ ] 修复文档已更新
