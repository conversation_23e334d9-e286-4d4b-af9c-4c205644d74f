import 'package:flutter/material.dart';
import '../../../core/utils/focus_data_repair.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_text_styles.dart';

/// 专注数据修复页面
class FocusDataRepairScreen extends StatefulWidget {
  const FocusDataRepairScreen({super.key});

  @override
  State<FocusDataRepairScreen> createState() => _FocusDataRepairScreenState();
}

class _FocusDataRepairScreenState extends State<FocusDataRepairScreen> {
  final FocusDataRepair _dataRepair = FocusDataRepair();
  
  bool _isLoading = false;
  String _reportText = '';
  int _duplicateCount = 0;

  @override
  void initState() {
    super.initState();
    _generateReport();
  }

  /// 生成修复报告
  Future<void> _generateReport() async {
    setState(() {
      _isLoading = true;
      _reportText = '正在检测重复数据...';
    });

    try {
      final duplicateGroups = await _dataRepair.detectDuplicateRecords();
      _duplicateCount = duplicateGroups.fold(0, (sum, group) => sum + group.length - 1);
      
      final report = await _dataRepair.generateRepairReport();
      
      setState(() {
        _reportText = report;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _reportText = '生成报告失败: $e';
        _isLoading = false;
      });
    }
  }

  /// 执行数据修复
  Future<void> _performRepair() async {
    if (_duplicateCount == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('没有发现重复数据，无需修复')),
      );
      return;
    }

    // 显示确认对话框
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认数据修复'),
        content: Text('将删除 $_duplicateCount 条重复记录，此操作不可撤销。确定继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('确定'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
      _reportText = '正在修复数据...';
    });

    try {
      final repairedCount = await _dataRepair.repairDuplicateRecords();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('数据修复完成，删除了 $repairedCount 条重复记录')),
      );
      
      // 重新生成报告
      await _generateReport();
    } catch (e) {
      setState(() {
        _reportText = '数据修复失败: $e';
        _isLoading = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('数据修复失败: $e')),
      );
    }
  }

  /// 验证数据完整性
  Future<void> _validateData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final isValid = await _dataRepair.validateDataIntegrity();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isValid ? '数据完整性验证通过' : '发现数据完整性问题，请查看日志'),
          backgroundColor: isValid ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('数据验证失败: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('数据修复工具'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('数据状态', style: AppTextStyles.headline3),
                    const SizedBox(height: 8),
                    if (_duplicateCount > 0)
                      Text(
                        '发现 $_duplicateCount 条重复记录',
                        style: AppTextStyles.bodyLarge.copyWith(color: Colors.orange),
                      )
                    else
                      Text(
                        '数据正常，无重复记录',
                        style: AppTextStyles.bodyLarge.copyWith(color: Colors.green),
                      ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 操作按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _generateReport,
                    icon: const Icon(Icons.refresh),
                    label: const Text('重新检测'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _performRepair,
                    icon: const Icon(Icons.build),
                    label: const Text('执行修复'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _duplicateCount > 0 ? Colors.orange : Colors.grey,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _validateData,
                    icon: const Icon(Icons.verified),
                    label: const Text('验证数据'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 报告内容
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text('检测报告', style: AppTextStyles.headline3),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _reportText,
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // 警告提示
            if (_duplicateCount > 0)
              Container(
                margin: const EdgeInsets.only(top: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  border: Border.all(color: Colors.orange.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '数据修复操作不可撤销，建议先备份数据',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.orange.shade800,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
