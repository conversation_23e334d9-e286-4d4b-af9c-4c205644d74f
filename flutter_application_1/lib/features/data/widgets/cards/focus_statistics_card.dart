import 'package:flutter/material.dart';
import '../../../../shared/theme/constants.dart';
import '../../utils/time_period_utils.dart';
import '../../../../core/models/focus_record.dart';
import 'focus_statistics_card_factory.dart';

/// 专注统计数据卡片
/// 显示特定时间周期内的专注统计数据
class FocusStatisticsCard extends StatelessWidget {
  final TimePeriod selectedPeriod;
  final double totalHours;
  final int sessionCount;
  final double avgMinutes;
  final List<FocusRecord> records; // 添加记录列表，用于计算打断次数
  final bool showTitle; // 是否显示标题（用于兼容旧代码）

  const FocusStatisticsCard({
    super.key,
    required this.selectedPeriod,
    required this.totalHours,
    required this.sessionCount,
    required this.avgMinutes,
    this.records = const [], // 默认为空列表
    this.showTitle = false, // 默认不在卡片内显示标题
  });

  @override
  Widget build(BuildContext context) {
    debugPrint('FocusStatisticsCard: 构建卡片 - 周期: $selectedPeriod, 总时长: ${totalHours}h, 专注次数: $sessionCount, 记录数: ${records.length}');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [AppShadows.low],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题（如果需要显示）
          if (showTitle) ...[
            Text(
              TimePeriodUtils.getPeriodTitle(selectedPeriod),
              style: AppTextStyles.headline3,
            ),
            const SizedBox(height: 16),
          ],

          // 使用工厂类创建卡片内容
          FocusStatisticsCardFactory.createContent(
            selectedPeriod: selectedPeriod,
            totalHours: totalHours,
            sessionCount: sessionCount,
            avgMinutes: avgMinutes,
            records: records,
          ),
        ],
      ),
    );
  }
}
