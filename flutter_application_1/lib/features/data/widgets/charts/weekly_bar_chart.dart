import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../core/models/focus_record.dart';
import '../../../../shared/theme/constants.dart';
import '../../utils/time_period_utils.dart';

/// 周视图专注时长柱状图
/// 显示周一到周日七天的专注时长数据，使用柱状图
class WeeklyBarChart extends StatefulWidget {
  final List<FocusRecord> records;
  final DateTime selectedDate;

  const WeeklyBarChart({
    super.key,
    required this.records,
    required this.selectedDate,
  });

  @override
  State<WeeklyBarChart> createState() => _WeeklyBarChartState();
}

class _WeeklyBarChartState extends State<WeeklyBarChart> {
  // 图表数据
  late List<DailyFocusData> _chartData;

  // 最大值
  late double _maxValue;

  @override
  void initState() {
    super.initState();
    _prepareChartData();
  }

  @override
  void didUpdateWidget(WeeklyBarChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.records != oldWidget.records ||
        widget.selectedDate != oldWidget.selectedDate) {
      _prepareChartData();
    }
  }

  // 准备图表数据
  void _prepareChartData() {
    debugPrint('WeeklyBarChart: 开始准备图表数据');
    debugPrint('WeeklyBarChart: 接收到的记录数量: ${widget.records.length}');
    debugPrint('WeeklyBarChart: 选择的日期: ${widget.selectedDate}');

    // 获取本周的开始日期（周一）
    final weekStart = TimePeriodUtils.getWeekStart(widget.selectedDate);
    debugPrint('WeeklyBarChart: 本周开始日期: $weekStart');

    // 创建本周七天的日期列表（周一到周日）
    final List<DateTime> weekDays = List.generate(
      7,
      (index) => weekStart.add(Duration(days: index))
    );

    debugPrint('WeeklyBarChart: 本周七天: ${weekDays.map((d) => DateFormat('yyyy-MM-dd').format(d)).join(', ')}');

    // 按日期分组记录
    final Map<String, List<FocusRecord>> recordsByDate = {};

    // 初始化本周七天的记录列表
    for (final day in weekDays) {
      final dateStr = DateFormat('yyyy-MM-dd').format(day);
      recordsByDate[dateStr] = [];
    }

    // 将记录按日期分组
    for (final record in widget.records) {
      final dateStr = DateFormat('yyyy-MM-dd').format(record.startTime);
      debugPrint('WeeklyBarChart: 处理记录 ${record.id}, 日期: $dateStr, 时长: ${record.durationSeconds}秒');

      if (recordsByDate.containsKey(dateStr)) {
        recordsByDate[dateStr]!.add(record);
        debugPrint('WeeklyBarChart: 记录已添加到 $dateStr');
      } else {
        debugPrint('WeeklyBarChart: 记录 $dateStr 不在本周范围内，跳过');
      }
    }

    // 计算每天的总专注时长
    final List<DailyFocusData> data = [];

    for (final day in weekDays) {
      final dateStr = DateFormat('yyyy-MM-dd').format(day);
      final dayRecords = recordsByDate[dateStr] ?? [];

      final totalSeconds = dayRecords.fold<int>(
        0, (sum, record) => sum + record.durationSeconds);

      final hours = totalSeconds / 3600.0;

      debugPrint('WeeklyBarChart: $dateStr - ${dayRecords.length}条记录, 总时长: ${hours.toStringAsFixed(2)}小时');

      data.add(DailyFocusData(
        date: day,
        hours: hours,
        dayOfWeek: _getDayOfWeekName(day.weekday),
      ));
    }

    _chartData = data;

    // 计算最大值
    _maxValue = _chartData.isEmpty
        ? 1.0
        : _chartData.map((e) => e.hours).reduce((a, b) => a > b ? a : b);

    // 确保最大值至少为1
    _maxValue = _maxValue < 1.0 ? 1.0 : _maxValue;

    // 为了美观，将最大值向上取整
    _maxValue = (_maxValue * 1.2).ceilToDouble();

    debugPrint('WeeklyBarChart: 图表数据准备完成，最大值: $_maxValue');
    debugPrint('WeeklyBarChart: 每日数据: ${data.map((d) => '${DateFormat('MM-dd').format(d.date)}: ${d.hours.toStringAsFixed(2)}h').join(', ')}');
  }

  // 获取星期几的名称
  String _getDayOfWeekName(int weekday) {
    switch (weekday) {
      case 1: return '周一';
      case 2: return '周二';
      case 3: return '周三';
      case 4: return '周四';
      case 5: return '周五';
      case 6: return '周六';
      case 7: return '周日';
      default: return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Y轴标签和图表主体
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Y轴标签 - 竖向排列并只保留数字
              Padding(
                padding: const EdgeInsets.only(right: 4.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    RotatedBox(
                      quarterTurns: 3,
                      child: Text('小时', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    ),
                    const SizedBox(height: 4),
                    Text('${_maxValue.toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(_maxValue * 0.75).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(_maxValue * 0.5).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('${(_maxValue * 0.25).toInt()}', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                    Text('0', style: TextStyle(fontSize: 10, color: Colors.grey.shade700)),
                  ],
                ),
              ),

              // 图表主体
              Expanded(
                child: CustomPaint(
                  painter: WeeklyBarChartPainter(
                    data: _chartData,
                    maxValue: _maxValue,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // X轴标签
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(
            _chartData.length,
            (index) => Text(
              _chartData[index].dayOfWeek,
              style: TextStyle(fontSize: 10, color: Colors.grey.shade700),
            ),
          ),
        ),
      ],
    );
  }
}

/// 每日专注数据
class DailyFocusData {
  final DateTime date;
  final double hours;
  final String dayOfWeek;

  DailyFocusData({
    required this.date,
    required this.hours,
    required this.dayOfWeek,
  });
}

/// 周视图柱状图绘制器
class WeeklyBarChartPainter extends CustomPainter {
  final List<DailyFocusData> data;
  final double maxValue;

  WeeklyBarChartPainter({
    required this.data,
    required this.maxValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制网格线
    _drawGrid(canvas, size);

    // 绘制柱状图
    _drawBars(canvas, size);

    // 绘制数据标签
    _drawLabels(canvas, size);
  }

  // 绘制网格线
  void _drawGrid(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.withAlpha(40)
      ..strokeWidth = 1;

    // 横线（Y轴刻度线）
    for (int i = 0; i <= 4; i++) {
      final y = size.height - (i * size.height / 4);
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // 竖线（分隔每天）
    for (int i = 0; i <= 7; i++) {
      final x = i * size.width / 7;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }
  }

  // 绘制柱状图
  void _drawBars(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final barWidth = size.width / 7 * 0.6; // 柱宽为每天宽度的60%

    for (int i = 0; i < data.length; i++) {
      final x = i * size.width / 7 + (size.width / 7 - barWidth) / 2;
      final height = data[i].hours / maxValue * size.height;
      final y = size.height - height;

      // 根据星期几选择不同的颜色
      final color = _getBarColor(data[i].date.weekday);

      // 柱状图画笔
      final paint = Paint()
        ..color = color.withAlpha(100)
        ..style = PaintingStyle.fill;

      // 绘制柱状图
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(x, y, barWidth, height),
          const Radius.circular(4),
        ),
        paint,
      );

      // 绘制柱状图边框
      final borderPaint = Paint()
        ..color = color
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;

      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(x, y, barWidth, height),
          const Radius.circular(4),
        ),
        borderPaint,
      );
    }
  }

  // 绘制数据标签
  void _drawLabels(Canvas canvas, Size size) {
    final textStyle = TextStyle(
      color: Colors.grey.shade700,
      fontSize: 10,
      fontWeight: FontWeight.bold,
    );

    for (int i = 0; i < data.length; i++) {
      if (data[i].hours <= 0) continue;

      final x = i * size.width / 7 + size.width / 14;
      final y = size.height - (data[i].hours / maxValue * size.height) - 15;

      final textSpan = TextSpan(
        text: '${data[i].hours.toStringAsFixed(1)}h',
        style: textStyle,
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: ui.TextDirection.ltr, // 从左到右的文本方向
        textAlign: TextAlign.center,
      );

      textPainter.layout();

      // 确保标签不超出顶部边界
      final adjustedY = y < textPainter.height ? 0 : y - textPainter.height / 2;

      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, adjustedY.toDouble()),
      );
    }
  }

  // 获取柱状图颜色
  Color _getBarColor(int weekday) {
    switch (weekday) {
      case 1: // 周一
      case 2: // 周二
      case 3: // 周三
      case 4: // 周四
      case 5: // 周五
        return AppColors.info; // 工作日使用主色
      case 6: // 周六
      case 7: // 周日
        return AppColors.primary; // 周末使用橙色
      default:
        return AppColors.info;
    }
  }

  @override
  bool shouldRepaint(WeeklyBarChartPainter oldDelegate) =>
    data != oldDelegate.data || maxValue != oldDelegate.maxValue;
}
