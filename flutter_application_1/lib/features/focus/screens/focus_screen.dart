import 'package:flutter/material.dart';
import 'package:circular_countdown_timer/circular_countdown_timer.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import '../../../shared/theme/constants.dart';
import '../../../core/models/subject_project.dart';
import '../../../core/models/focus_record.dart';
import '../../../core/models/project_progress_change.dart';
import '../../../core/models/focus_session.dart';
import '../../../core/services/enhanced_hive_service.dart';
import '../../../core/services/preferences_service.dart';
import '../../../core/services/focus_session_service.dart';

import '../../task/providers/subject_state.dart';
import '../widgets/long_press_button.dart';
import 'dart:async';

/// 专注页面
/// 用于显示专注计时器、控制按钮和相关信息
/// 支持倒计时和正计时两种模式
class FocusScreen extends ConsumerStatefulWidget {
  final bool isCountdown; // 是否为倒计时模式
  final double? countdownMinutes; // 倒计时分钟数（仅在倒计时模式下使用）
  final Subject subject; // 当前选择的科目
  final Project project; // 当前选择的项目
  final bool isRecovering; // 是否为恢复模式（阶段4新增）

  const FocusScreen({
    super.key,
    required this.isCountdown,
    this.countdownMinutes,
    required this.subject,
    required this.project,
    this.isRecovering = false, // 默认为false
  });

  @override
  ConsumerState<FocusScreen> createState() => _FocusScreenState();
}

/// 计时器状态管理类
/// 用于管理计时器的状态，与UI分离
class TimerState extends ChangeNotifier {
  // 计时器控制器
  final CountDownController controller = CountDownController();

  // 计时器状态
  bool _isRunning = false;
  bool _isPaused = false;
  bool _isCompleted = false;

  // 计时开始时间（用于正计时模式）
  DateTime? _startTime;

  // 正计时模式下的计时器
  int _elapsedSeconds = 0;
  Timer? _forwardTimer;

  // 应用进入后台的时间
  DateTime? _backgroundTime;

  // 阶段3新增：基于时间戳的备份计算（预留字段）

  // 最大计时时间（6小时 = 21600秒）
  static const int maxForwardTimerSeconds = 6 * 60 * 60;

  bool get isRunning => _isRunning;
  bool get isPaused => _isPaused;
  bool get isCompleted => _isCompleted;
  int get elapsedSeconds => _elapsedSeconds;

  // 正计时模式下的显示时间
  String get elapsedTimeText {
    final hours = _elapsedSeconds ~/ 3600;
    final minutes = (_elapsedSeconds % 3600) ~/ 60;
    final seconds = _elapsedSeconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  // 启动正计时
  void startForwardTimer() {
    _startTime = DateTime.now();
    _isRunning = true;
    _isPaused = false;
    _backgroundTime = null;

    _forwardTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // 如果有后台时间记录，需要补偿后台时间
      if (_backgroundTime != null) {
        final backgroundDuration = DateTime.now().difference(_backgroundTime!);
        _elapsedSeconds += backgroundDuration.inSeconds;
        _backgroundTime = null;
        debugPrint('应用回到前台，补偿后台时间: ${backgroundDuration.inSeconds}秒');
      } else {
        _elapsedSeconds++;
      }

      // 阶段3新增：每10秒验证一次Timer状态
      if (_elapsedSeconds % 10 == 0) {
        validateAndCorrectTimerState();
      }

      // 检查是否达到最大时长限制（6小时）
      if (_elapsedSeconds >= maxForwardTimerSeconds) {
        // 达到最大时长，自动完成
        timer.cancel();
        _isRunning = true;
        _isPaused = false;
        _isCompleted = true;
      }

      notifyListeners();
    });
  }

  // 暂停正计时
  void pauseForwardTimer() {
    _forwardTimer?.cancel();
    _isPaused = true;
    notifyListeners();
  }

  // 恢复正计时
  void resumeForwardTimer() {
    _isPaused = false;
    _backgroundTime = null;
    _forwardTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // 如果有后台时间记录，需要补偿后台时间
      if (_backgroundTime != null) {
        final backgroundDuration = DateTime.now().difference(_backgroundTime!);
        _elapsedSeconds += backgroundDuration.inSeconds;
        _backgroundTime = null;
        debugPrint('应用回到前台，补偿后台时间: ${backgroundDuration.inSeconds}秒');
      } else {
        _elapsedSeconds++;
      }

      // 阶段3新增：每10秒验证一次Timer状态
      if (_elapsedSeconds % 10 == 0) {
        validateAndCorrectTimerState();
      }

      // 检查是否达到最大时长限制（6小时）
      if (_elapsedSeconds >= maxForwardTimerSeconds) {
        // 达到最大时长，自动完成
        timer.cancel();
        _isRunning = true;
        _isPaused = false;
        _isCompleted = true;
      }

      notifyListeners();
    });
  }

  // 记录应用进入后台的时间
  void recordBackgroundTime() {
    if (_isRunning && !_isPaused) {
      _backgroundTime = DateTime.now();
      debugPrint('应用进入后台，记录时间: $_backgroundTime, 当前已过时间: $_elapsedSeconds秒');
    }
  }

  // 阶段3新增：基于时间戳的备份计算
  int calculateElapsedSecondsFromTimestamp() {
    if (_startTime == null) return _elapsedSeconds;

    final now = DateTime.now();
    final totalElapsed = now.difference(_startTime!).inSeconds;

    // 返回基于开始时间的总经过时间
    return totalElapsed;
  }

  // 阶段3新增：验证并修正计时器状态
  void validateAndCorrectTimerState() {
    if (_startTime == null || !_isRunning || _isPaused) return;

    final timestampElapsed = calculateElapsedSecondsFromTimestamp();
    final timerElapsed = _elapsedSeconds;

    // 如果时间戳计算的时间比Timer计算的时间多超过5秒，说明Timer可能有问题
    if (timestampElapsed > timerElapsed + 5) {
      debugPrint('检测到Timer时间异常，Timer: ${timerElapsed}s, 时间戳: ${timestampElapsed}s');
      debugPrint('使用时间戳修正Timer状态');
      _elapsedSeconds = timestampElapsed;
      notifyListeners();
    }
  }

  // 设置计时器状态
  void setTimerState({required bool isRunning, required bool isPaused, required bool isCompleted}) {
    _isRunning = isRunning;
    _isPaused = isPaused;
    _isCompleted = isCompleted;
    notifyListeners();
  }

  // 阶段4新增：设置已过时间（用于恢复）
  void setElapsedSeconds(int seconds) {
    _elapsedSeconds = seconds;
    notifyListeners();
  }

  @override
  void dispose() {
    _forwardTimer?.cancel();
    super.dispose();
  }
}

class _FocusScreenState extends ConsumerState<FocusScreen> with WidgetsBindingObserver {
  // Hive服务
  final EnhancedHiveService _hiveService = EnhancedHiveService();
  // 偏好设置服务
  final PreferencesService _preferencesService = PreferencesService();
  // 专注会话服务
  final FocusSessionService _focusSessionService = FocusSessionService();

  // 专注开始时间
  DateTime? _focusStartTime;
  // 计时器状态管理
  late final TimerState _timerState;

  // 当前专注会话（阶段2新增）
  FocusSession? _currentFocusSession;

  //常亮

  // 当前视图模式（默认为显示名称模式）
  bool _isSimpleView = false;

  // 专注完成后的界面状态
  bool _showCompletionUI = false; // 是否显示完成后的界面
  bool _progressAdjusted = false; // 是否已经调整过进度
  bool _hasTriedToExit = false; // 是否已经尝试过退出
  bool _isInputEnabled = true; // 是否允许输入
  bool _isOverThreeMinutes = false; // 是否超过60秒
  Timer? _threeMinutesTimer; // 60秒计时器
  final TextEditingController _progressController = TextEditingController(text: '1'); // 进度输入控制器
  FocusRecord? _lastSavedFocusRecord; // 最后保存的专注记录

  // 防重复保存标记
  bool _focusRecordSaved = false;

  // 进度调整暂存状态
  int? _tempProgressValue; // 暂存的进度值
  bool _hasUnsavedProgress = false; // 是否有未保存的进度

  // 使用GlobalKey保持计时器状态
  final GlobalKey<State> _countdownTimerKey = GlobalKey<State>();
  final GlobalKey<State> _forwardTimerKey = GlobalKey<State>();

  // 是否开启常亮（默认开启，然后异步加载真实值）
  bool _isKeepScreenOn = true;

  // 修改初始化方法
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _timerState = TimerState();

    // 保存当前选择的科目和项目
    _saveCurrentSelection();

    // 阶段4：如果是恢复模式，恢复专注会话状态
    if (widget.isRecovering) {
      _recoverFocusSession();
    } else {
      // 只有新建会话时才设置专注开始时间
      _focusStartTime = DateTime.now();

      // 创建并保存专注会话（阶段2新增）
      _createAndSaveFocusSession();

      // 只有新建会话时才启动60秒计时器
      _startSixtySecondsTimer();
    }

    // 延迟加载屏幕常亮设置，避免影响恢复流程
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadKeepScreenOnSetting();
    });

    // 如果是正计时模式，启动正计时
    if (!widget.isCountdown) {
      _timerState.startForwardTimer();
      // 正计时模式下在Timer启动后更新屏幕常亮状态
      _updateWakelock();
    }
    // 倒计时模式下不在此处调用_updateWakelock()，
    // 而是在用户点击开始按钮时调用
  }

  // 加载屏幕常亮设置
  void _loadKeepScreenOnSetting() async {
    try {
      final savedSetting = await _preferencesService.getKeepScreenOnSetting();
      if (mounted) {
        setState(() {
          _isKeepScreenOn = savedSetting;
        });
        // 加载完成后更新屏幕常亮状态
        _updateWakelock();
        debugPrint('加载屏幕常亮设置: $_isKeepScreenOn');
      }
    } catch (e) {
      debugPrint('加载屏幕常亮设置失败: $e');
      // 保持默认值true，不需要额外处理
    }
  }

  // 保存屏幕常亮设置
  void _saveKeepScreenOnSetting(bool value) async {
    try {
      await _preferencesService.saveKeepScreenOnSetting(value);
      debugPrint('保存屏幕常亮设置: $value');
    } catch (e) {
      debugPrint('保存屏幕常亮设置失败: $e');
    }
  }

  // 更新屏幕常亮状态
  void _updateWakelock() {
    // 只有在计时器运行且未暂停时才保持屏幕常亮
    if (_timerState.isRunning && !_timerState.isPaused && _isKeepScreenOn) {
      WakelockPlus.enable();
    } else {
      WakelockPlus.disable();
    }
  }

  // 同步更新Riverpod项目状态
  void _updateRiverpodProjectState(Project updatedProject) {
    try {
      // 获取当前Riverpod状态中的项目列表
      final currentProjects = ref.read(subjectStateProvider).projects;

      // 找到并更新对应的项目
      final updatedProjects = currentProjects.map((project) {
        if (project.id == updatedProject.id) {
          return updatedProject;
        }
        return project;
      }).toList();

      // 更新Riverpod状态
      ref.read(subjectStateProvider.notifier).setProjects(updatedProjects);

      // 如果当前项目就是被更新的项目，也要更新当前项目状态
      final currentProject = ref.read(subjectStateProvider).currentProject;
      if (currentProject?.id == updatedProject.id) {
        ref.read(subjectStateProvider.notifier).setCurrentProject(updatedProject);
      }

      debugPrint('Riverpod项目状态已同步更新: ${updatedProject.name}');
    } catch (e) {
      debugPrint('更新Riverpod项目状态失败: $e');
    }
  }

  // 启动60秒计时器
  void _startSixtySecondsTimer() {
    // 取消已有的计时器
    _threeMinutesTimer?.cancel();

    // 重置状态
    setState(() {
      _isOverThreeMinutes = false;
    });

    // 启动60秒计时器，60秒后更新状态
    _threeMinutesTimer = Timer(const Duration(seconds: 60), () {
      if (mounted) {
        setState(() {
          _isOverThreeMinutes = true;
        });
      }
    });
  }

  // 专注完成通知功能已移除
  // 保留方法注释以备后续版本使用


  // 保存当前选择的科目和项目
  void _saveCurrentSelection() {
    _preferencesService.saveLastSelection(widget.subject.id, widget.project.id);

    // 使用Future.microtask确保在构建完成后更新状态
    Future.microtask(() {
      // 同时更新Riverpod状态
      ref.read(subjectStateProvider.notifier).setCurrentSubject(widget.subject);
      ref.read(subjectStateProvider.notifier).setCurrentProject(widget.project);
    });
  }

  // 创建并保存专注会话（阶段2新增）
  void _createAndSaveFocusSession() {
    try {
      // 创建专注会话
      _currentFocusSession = _focusSessionService.createFocusSession(
        subjectId: widget.subject.id,
        projectId: widget.project.id,
        isCountdown: widget.isCountdown,
        countdownDurationSeconds: widget.isCountdown && widget.countdownMinutes != null
            ? (widget.countdownMinutes! * 60).toInt()
            : null,
      );

      // 异步保存会话状态
      _focusSessionService.saveFocusSession(_currentFocusSession!).then((_) {
        debugPrint('专注会话已保存: ${_currentFocusSession!.id}');
      }).catchError((error) {
        debugPrint('保存专注会话失败: $error');
      });
    } catch (e) {
      debugPrint('创建专注会话失败: $e');
    }
  }

  // 阶段4：恢复专注会话状态
  void _recoverFocusSession() async {
    try {
      debugPrint('阶段4：开始恢复专注会话状态');

      // 获取当前的专注会话
      final session = await _focusSessionService.getCurrentFocusSession();
      if (session == null) {
        debugPrint('阶段4：没有找到专注会话，创建新会话');
        _createAndSaveFocusSession();
        return;
      }

      debugPrint('阶段4：恢复专注会话: ${session.id}');
      debugPrint('  - 已过时间: ${session.currentElapsedSeconds}秒');
      debugPrint('  - 是否暂停: ${session.isPaused}');

      // 设置当前专注会话
      _currentFocusSession = session;

      // 恢复专注开始时间
      _focusStartTime = session.startTime;
      debugPrint('阶段4：恢复专注开始时间: $_focusStartTime');

      // 恢复Timer状态
      final elapsedSeconds = session.currentElapsedSeconds;

      // 根据实际专注时间设置60秒状态
      if (elapsedSeconds >= 60) {
        // 如果已经专注超过60秒，直接设置为超过60秒状态
        setState(() {
          _isOverThreeMinutes = true;
        });
        debugPrint('阶段4：已专注$elapsedSeconds秒，跳过60秒限制');
      } else {
        // 如果专注时间不足60秒，启动剩余时间的计时器
        final remainingTime = 60 - elapsedSeconds;
        _threeMinutesTimer = Timer(Duration(seconds: remainingTime), () {
          if (mounted) {
            setState(() {
              _isOverThreeMinutes = true;
            });
          }
        });
        debugPrint('阶段4：已专注$elapsedSeconds秒，还需$remainingTime秒到达60秒限制');
      }

      if (widget.isCountdown) {
        // 倒计时模式：设置剩余时间
        final remainingSeconds = session.remainingSeconds;
        debugPrint('阶段4：恢复倒计时，剩余时间: $remainingSeconds秒');

        if (remainingSeconds <= 0) {
          // 倒计时已完成
          debugPrint('阶段4：倒计时已完成，显示完成界面');
          _timerState.setTimerState(
            isRunning: false,
            isPaused: false,
            isCompleted: true,
          );
          setState(() {
            _showCompletionUI = true;
          });
        } else {
          // 恢复倒计时状态
          _timerState.setTimerState(
            isRunning: !session.isPaused,
            isPaused: session.isPaused,
            isCompleted: false,
          );

          // 关键修复：设置倒计时器从正确的剩余时间开始
          debugPrint('阶段4：设置倒计时器剩余时间: $remainingSeconds秒');

          // 延迟执行，确保UI已经构建完成
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              // 重置倒计时器到正确的剩余时间
              _timerState.controller.restart(duration: remainingSeconds);

              // 如果会话未暂停，启动倒计时器
              if (!session.isPaused) {
                _timerState.controller.start();
                debugPrint('阶段4：倒计时器已启动，剩余时间: $remainingSeconds秒');
              } else {
                _timerState.controller.pause();
                debugPrint('阶段4：倒计时器已暂停，剩余时间: $remainingSeconds秒');
              }
            }
          });
        }
      } else {
        // 正计时模式：恢复已过时间
        debugPrint('阶段4：恢复正计时，已过时间: $elapsedSeconds秒');

        _timerState.setTimerState(
          isRunning: !session.isPaused,
          isPaused: session.isPaused,
          isCompleted: false,
        );

        // 设置已过时间
        _timerState.setElapsedSeconds(elapsedSeconds);
      }

      // 如果会话是暂停状态，更新UI
      if (session.isPaused) {
        debugPrint('阶段4：会话处于暂停状态');
      } else {
        debugPrint('阶段4：会话处于运行状态');
      }

      debugPrint('阶段4：专注会话状态恢复完成');

      // 恢复完成后更新屏幕常亮状态
      _updateWakelock();

    } catch (e) {
      debugPrint('阶段4：恢复专注会话失败: $e');
      // 恢复失败，创建新会话
      _createAndSaveFocusSession();
    }
  }

  // 清理专注会话（阶段2新增）
  Future<void> _clearFocusSession() async {
    try {
      if (_currentFocusSession != null) {
        await _focusSessionService.clearFocusSession();
        _currentFocusSession = null;
        debugPrint('专注会话已清理');
      }
    } catch (e) {
      debugPrint('清理专注会话失败: $e');
    }
  }

  // 阶段3新增：应用恢复时验证Timer状态
  void _validateTimerStateOnResume() {
    if (!widget.isCountdown && _timerState.isRunning && !_timerState.isPaused) {
      // 只对正计时模式进行验证
      _timerState.validateAndCorrectTimerState();

      // 如果专注会话存在，也进行交叉验证
      if (_currentFocusSession != null) {
        final sessionElapsed = _currentFocusSession!.currentElapsedSeconds;
        final timerElapsed = _timerState.elapsedSeconds;

        // 如果会话计算的时间与Timer差异过大，记录警告
        if ((sessionElapsed - timerElapsed).abs() > 10) {
          debugPrint('检测到会话与Timer时间差异: 会话=${sessionElapsed}s, Timer=${timerElapsed}s');
        }
      }
    }
  }

  // 同步专注会话状态（阶段2新增）
  void _syncFocusSessionState() {
    if (_currentFocusSession == null) return;

    try {
      // 根据当前Timer状态更新专注会话
      FocusSession updatedSession;

      if (_timerState.isPaused) {
        // 如果Timer暂停，则暂停会话
        updatedSession = _currentFocusSession!.pause();
      } else {
        // 如果Timer运行，则恢复会话
        updatedSession = _currentFocusSession!.resume();
      }

      // 异步保存更新的会话状态
      _focusSessionService.saveFocusSession(updatedSession).then((_) {
        _currentFocusSession = updatedSession;
        debugPrint('专注会话状态已同步: isPaused=${updatedSession.isPaused}');
      }).catchError((error) {
        debugPrint('同步专注会话状态失败: $error');
      });
    } catch (e) {
      debugPrint('同步专注会话状态异常: $e');
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _timerState.dispose();
    _progressController.dispose();

    // 取消60秒计时器
    _threeMinutesTimer?.cancel();

    // 清理专注会话（阶段2新增）
    _clearFocusSession();

    // 确保在页面销毁时关闭屏幕常亮
    WakelockPlus.disable();

    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 处理应用生命周期变化
    if (state == AppLifecycleState.paused) {
      // 应用进入后台
      // 关闭屏幕常亮，但保持计时器继续运行
      WakelockPlus.disable();
      // 记录进入后台的时间，用于后续补偿计时
      _timerState.recordBackgroundTime();

      // 同步专注会话状态（阶段2新增）
      _syncFocusSessionState();
    } else if (state == AppLifecycleState.resumed) {
      // 应用回到前台
      // 根据设置恢复屏幕常亮
      _updateWakelock();
      // 计时器会在下次tick时自动补偿后台时间

      // 阶段3新增：验证Timer状态
      _validateTimerStateOnResume();

      // 同步专注会话状态（阶段2新增）
      _syncFocusSessionState();
    }
  }

  // 结束专注
  Future<void> _endFocus() async {
    debugPrint('🔚 开始结束专注流程');
    debugPrint('  - 是否倒计时模式: ${widget.isCountdown}');
    debugPrint('  - 倒计时是否完成: ${_timerState.isCompleted}');
    debugPrint('  - 是否超过60秒: $_isOverThreeMinutes');
    debugPrint('  - 专注记录是否已保存: $_focusRecordSaved');

    // 清理专注会话（阶段2新增）
    await _clearFocusSession();

    // 如果已经显示完成界面，则处理退出逻辑
    if (_showCompletionUI) {
      // 如果有未保存的进度，先保存
      if (_hasUnsavedProgress && _tempProgressValue != null) {
        await _saveProgressChange();
      }

      // 如果是自定义模式且需要调整进度，检查是否已调整
      if (widget.project.isTrackingEnabled &&
          widget.project.trackingMode == ProgressTrackingMode.custom &&
          !_progressAdjusted) {

        // 如果已经尝试过退出，则直接退出
        if (_hasTriedToExit) {
          if (context.mounted) {
            Navigator.of(context).pop();
          }
          return;
        }

        // 第一次尝试退出，提示用户并标记已尝试
        setState(() {
          _hasTriedToExit = true;
        });

        // 提示用户调整进度
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('您尚未更新进度，再次点击"退出"将直接退出')),
        );
        return;
      }

      // 直接返回上一页
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      return;
    }

    // 移除这里的60秒检查，让_saveFocusRecord方法内部处理
    // 这样可以确保保存逻辑的一致性

    // 如果是正计时模式且超过60秒，直接保存记录并处理后续逻辑
    if (!widget.isCountdown) {
      // 先保存专注记录（所有模式都需要保存）
      await _saveFocusRecord();

      // 移除通知功能调用
      // await _sendFocusCompletionNotification();

      // 如果项目是自定义进度模式，显示进度调整界面
      if (widget.project.isTrackingEnabled &&
          widget.project.trackingMode == ProgressTrackingMode.custom) {
        setState(() {
          _showCompletionUI = true;
        });
        return;
      }

      // 其他情况（专注时间模式或未开启进度追踪），直接退出
      if (context.mounted) {
        Navigator.of(context).pop();
      }
      return;
    }

    // 检查是否是倒计时模式且未完成（中断）
    final bool isCountdownInterrupted = widget.isCountdown && !_timerState.isCompleted;

    // 准备对话框内容
    String dialogTitle = '结束专注';
    String dialogContent = '确定要结束当前专注吗？';

    // 如果是倒计时中断，显示特殊提示
    if (isCountdownInterrupted) {
      dialogContent = '倒计时未完成，专注时间将不计入项目进度\n\n确定要结束吗？';
    }

    // 显示确认对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(dialogTitle),
        content: Text(dialogContent),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context); // 关闭对话框

              // 保存专注记录
              await _saveFocusRecord();

              // 移除通知功能调用
              // await _sendFocusCompletionNotification();

              // 如果是倒计时完成且项目是自定义进度模式，显示进度调整界面
              if (_timerState.isCompleted &&
                  widget.project.isTrackingEnabled &&
                  widget.project.trackingMode == ProgressTrackingMode.custom) {
                setState(() {
                  _showCompletionUI = true;
                });
                return;
              }

              // 返回上一页
              if (!mounted) return;
              // 使用安全的方式返回
              if (context.mounted) {
                Navigator.of(context).pop(); // 返回上一页
              }
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 保存专注记录
  Future<void> _saveFocusRecord() async {
    debugPrint('💾 开始保存专注记录流程');
    debugPrint('  - 当前时间: ${DateTime.now()}');
    debugPrint('  - 专注开始时间: $_focusStartTime');
    debugPrint('  - 是否超过60秒: $_isOverThreeMinutes');
    debugPrint('  - 记录是否已保存: $_focusRecordSaved');

    try {
      // 更安全的防重复保存检查：基于记录内容而非简单标记
      if (_lastSavedFocusRecord != null &&
          _lastSavedFocusRecord!.startTime == _focusStartTime) {
        debugPrint('❌ 检测到相同开始时间的记录已存在，跳过重复保存');
        debugPrint('  - 已保存记录ID: ${_lastSavedFocusRecord!.id}');
        debugPrint('  - 开始时间: $_focusStartTime');
        return;
      }

      // 确保Hive服务已初始化
      await _hiveService.initHive();

      // 检查是否在专注开始后60秒内结束
      if (!_isOverThreeMinutes) {
        // 60秒内结束，不计入统计
        debugPrint('❌ 专注时间少于60秒，不保存记录');
        return; // 直接返回，不保存记录
      }

      // 优先使用专注会话的准确时长计算
      int durationSeconds;
      if (_currentFocusSession != null) {
        // 使用专注会话的准确时长（已正确处理暂停时间）
        durationSeconds = _currentFocusSession!.currentElapsedSeconds;
        debugPrint('使用专注会话计算时长: ${durationSeconds}秒');
      } else {
        // 备用计算方式
        durationSeconds = widget.isCountdown
            ? (widget.countdownMinutes != null ? (widget.countdownMinutes! * 60).toInt() : 0)
            : _timerState.elapsedSeconds;
        debugPrint('使用Timer状态计算时长: ${durationSeconds}秒');
      }

      // 检查是否是倒计时模式且未完成（中断）
      final bool isCountdownInterrupted = widget.isCountdown && !_timerState.isCompleted;

      // 计算实际记录的时间（如果是倒计时中断，则记录实际的时间）
      final int actualDurationSeconds = isCountdownInterrupted
          ? (_currentFocusSession?.currentElapsedSeconds ?? _timerState.elapsedSeconds)
          : durationSeconds;

      // 获取当前时间
      final DateTime now = DateTime.now();

      // 创建专注记录
      final focusRecord = FocusRecord(
        id: now.millisecondsSinceEpoch.toString(),
        projectId: widget.project.id,
        subjectId: widget.subject.id,
        startTime: _focusStartTime ?? now,
        endTime: now,
        durationSeconds: actualDurationSeconds, // 使用实际时间
        isCountdown: widget.isCountdown,
        plannedDurationMinutes: widget.countdownMinutes?.toInt(),
        status: widget.isCountdown
            ? (_timerState.isCompleted ? FocusRecordStatus.completed : FocusRecordStatus.interrupted)
            : FocusRecordStatus.completed, // 正计时模式始终为完成状态
        interruptionCount: (widget.isCountdown && !_timerState.isCompleted) ? 1 : 0, // 只有倒计时中断才记录中断次数
        notes: null,
      );

      // 保存专注记录
      await _hiveService.focusRecordRepository.saveFocusRecord(focusRecord);

      // 保存最后的专注记录引用
      _lastSavedFocusRecord = focusRecord;

      // 设置已保存标记，防止重复保存
      _focusRecordSaved = true;

      debugPrint('✅ 专注记录保存成功:');
      debugPrint('  - 记录ID: ${focusRecord.id}');
      debugPrint('  - 时长: ${actualDurationSeconds}秒 (${(actualDurationSeconds/3600).toStringAsFixed(2)}小时)');
      debugPrint('  - 状态: ${focusRecord.status}');
      debugPrint('  - 项目: ${widget.project.name}');
      debugPrint('  - 科目: ${widget.subject.name}');

      // 计算专注时长（小时），保留两位小数
      final double focusHours = actualDurationSeconds / 3600.0;

      // 准备提示信息
      String message = '已保存 ${focusHours.toStringAsFixed(2)} 小时的专注记录';

      // 初始化项目更新对象，同时更新新旧两套字段
      final double newCurrentFocusHours = (widget.project.currentFocusHours ?? 0) + focusHours;
      Project updatedProject = widget.project.copyWith(
        focusedHours: widget.project.focusedHours + focusHours, // 旧字段
        currentFocusHours: newCurrentFocusHours, // 新字段
      );

      // 如果项目开启了进度追踪
      if (widget.project.isTrackingEnabled) {
        // 如果是专注时间模式
        if (widget.project.trackingMode == ProgressTrackingMode.focusTime) {
          // 如果是倒计时模式且未完成，不计入进度
          if (isCountdownInterrupted) {
            // 不更新进度，不显示额外提示（已在对话框中提示）
          } else {
            // 计算新进度，确保不超过1.0
            double newProgress = 0.0;
            if (widget.project.totalFocusHours != null && widget.project.totalFocusHours! > 0) {
              newProgress = (newCurrentFocusHours / widget.project.totalFocusHours!).clamp(0.0, 1.0);
            }

            // 更新项目对象，添加进度信息
            updatedProject = updatedProject.copyWith(
              progress: newProgress,
            );

            // 添加进度更新信息到提示中
            // final int progressPercent = (newProgress * 100).round();
            // message += '\n项目进度已更新为 $progressPercent%';

            // 如果进度达到100%，添加祝贺信息
            if (newProgress >= 1.0) {
              message += '\n恭喜您完成了项目目标！';
            }
          }
        }
        // 自定义模式下，专注时间已在上面统一更新，不需要额外处理
      }

      // 保存更新后的项目
      await _hiveService.subjectRepository.saveProject(updatedProject);

      // 同步更新Riverpod状态，确保项目列表实时更新
      _updateRiverpodProjectState(updatedProject);

      // 添加详细的调试日志
      debugPrint('专注记录保存完成，项目数据已更新:');
      debugPrint('  - 项目: ${updatedProject.name}');
      debugPrint('  - focusedHours: ${updatedProject.focusedHours}h');
      debugPrint('  - currentFocusHours: ${updatedProject.currentFocusHours}h');
      debugPrint('  - 本次专注时间: ${focusHours}h');

      // 移除用户可见的成功提示，保留debugPrint日志
      debugPrint('专注记录保存成功: $message');
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存专注记录失败: $e')),
        );
      }
    }
  }

  // 保存进度变化
  Future<void> _saveProgressChange() async {
    if (!_hasUnsavedProgress || _tempProgressValue == null) {
      return;
    }

    try {
      // 获取自定义单位和目标值
      final String unit = widget.project.customUnit ?? '单位';
      final int targetValue = widget.project.targetValue ?? 100;
      final int currentValue = widget.project.currentCustomValue ?? 0;
      final int newCustomValue = _tempProgressValue!;

      // 计算新进度
      final double newProgress = targetValue > 0 ?
          (newCustomValue / targetValue).clamp(0.0, 1.0) : 0.0;

      // 获取当前项目的最新数据（包含专注时间）
      await _hiveService.initHive();
      final currentProject = _hiveService.subjectRepository.getProjectById(widget.project.id) ?? widget.project;

      // 更新项目
      final updatedProject = currentProject.copyWith(
        progress: newProgress,
        currentCustomValue: newCustomValue,
      );

      // 保存更新后的项目
      await _hiveService.subjectRepository.saveProject(updatedProject);

      // 同步更新Riverpod状态，确保项目列表实时更新
      _updateRiverpodProjectState(updatedProject);

      // 记录进度变化
      final progressChange = ProjectProgressChange(
        id: 'progress_${DateTime.now().millisecondsSinceEpoch}',
        projectId: widget.project.id,
        timestamp: DateTime.now(),
        previousValue: currentValue.toDouble(),
        newValue: newCustomValue.toDouble(),
        previousProgress: widget.project.progress,
        newProgress: newProgress,
        source: ProgressChangeSource.focusCompletion,
        focusRecordId: _lastSavedFocusRecord?.id, // 关联专注记录ID
      );

      // 保存进度变化记录
      await _hiveService.projectProgressRepository.saveProgressChange(progressChange);
      debugPrint('项目进度变化已记录: ${progressChange.id}, 来源: 专注完成后调整, 变化: +${newCustomValue - currentValue} $unit');

      // 清除暂存状态
      setState(() {
        _hasUnsavedProgress = false;
        _tempProgressValue = null;
      });

      // 移除用户可见的成功提示，保留debugPrint日志
      debugPrint('进度已保存为 $newCustomValue $unit');
    } catch (e) {
      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存进度失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // 禁止默认的返回行为
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // 拦截返回操作，调用结束专注方法
          _endFocus();
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true, // 启用键盘适配
        body: GestureDetector(
          onTap: () {
            // 点击空白区域隐藏键盘
            FocusScope.of(context).unfocus();
          },
          child: Container(
            decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color.fromARGB(255, 204, 255, 229), // 顶部颜色
              Color.fromARGB(255, 255, 250, 240), // 底部颜色
            ],
          ),
            ),
            child: SafeArea(
          child: Column(
            children: [
              // 顶部导航栏
              _buildTopBar(),

              // 中间内容区域
              Expanded(
                child: Column(
                  children: [
                    // 添加顶部空间，使计时器更居中
                    const SizedBox(height: 20),

                    // 上部信息
                    AnimatedOpacity(
                      opacity: _isSimpleView ? 0.0 : 1.0,
                      duration: const Duration(milliseconds: 300),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0), // 减小垂直间距
                        child: Column(
                          children: [
                            Text(
                              widget.subject.name,
                              style: AppTextStyles.headline2,
                            ),
                            const SizedBox(height: 4), // 减小间距
                            Text(
                              widget.project.name,
                              style: AppTextStyles.bodyLarge.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // 中间计时器或进度调整组件
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 40), // 向上移动计时器
                        child: Center(
                          child: _showCompletionUI && widget.project.isTrackingEnabled &&
                                 widget.project.trackingMode == ProgressTrackingMode.custom
                              ? _buildProgressAdjustmentWidget()
                              : widget.isCountdown
                                  ? _buildCountdownTimer()
                                  : _buildForwardTimer(),
                        ),
                      ),
                    ),

                    // 底部状态文本（暂时不使用状态文本）
                    // AnimatedOpacity(
                    //   opacity: _isSimpleView ? 0.0 : 1.0,
                    //   duration: const Duration(milliseconds: 300),
                    //   child: AnimatedBuilder(
                    //     animation: _timerState,
                    //     builder: (context, _) {
                    //       return Padding(
                    //         padding: const EdgeInsets.symmetric(vertical: 24.0),
                    //         child: Text(
                    //           _timerState.isCompleted
                    //               ? '专注完成！'
                    //               : _timerState.isPaused
                    //                   ? '已暂停'
                    //                   : '专注中...',
                    //           style: AppTextStyles.bodyLarge.copyWith(
                    //             color: _timerState.isCompleted
                    //                 ? AppColors.success
                    //                 : _timerState.isPaused
                    //                     ? AppColors.warning
                    //                     : AppColors.primary,
                    //             fontWeight: FontWeight.w600,
                    //           ),
                    //         ),
                    //       );
                    //     },
                    //   ),
                    // ),
                  ],
                ),
              ),

              // 底部控制按钮
              _buildBottomControls(),
            ],
          ),
            ),
          ),
        ), // GestureDetector结束
      ), // Scaffold结束
    ); // PopScope结束
  }

  // 构建顶部导航栏
  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 移除返回按钮，使用空白占位
          const SizedBox(width: 48), // 保持布局平衡


          // 右侧按钮组
          Row(
            children: [
              // 视图切换按钮
              IconButton(
                icon: Icon(
                  _isSimpleView ? Icons.visibility_off : Icons.visibility,
                  color: AppColors.text,
                ),
                onPressed: () {
                  // 只更新视图模式状态，不触发计时器重建
                  setState(() {
                    _isSimpleView = !_isSimpleView;
                  });
                },
              ),

              // 设置按钮
              IconButton(
                icon: const Icon(Icons.settings, color: AppColors.text),
                onPressed: () {
                  // 显示设置菜单
                  showModalBottomSheet(
                    context: context,
                    builder: (BuildContext sheetContext) => StatefulBuilder(
                      builder: (BuildContext context, StateSetter setSheetState) {
                        return Container(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // 移除完成时提醒设置选项
                              ListTile(
                                leading: const Icon(Icons.brightness_7),
                                title: const Text('保持屏幕常亮'),
                                trailing: Switch(
                                  value: _isKeepScreenOn,
                                  onChanged: (value) {
                                    // 更新底部弹窗内的状态
                                    setSheetState(() {
                                      _isKeepScreenOn = value;
                                    });
                                    // 同时更新外部状态
                                    setState(() {
                                      _updateWakelock();
                                    });
                                    // 保存设置到本地存储
                                    _saveKeepScreenOnSetting(value);
                                  },
                                  activeColor: AppColors.primary,
                                ),
                                onTap: () {
                                  // 点击整行也可以切换开关
                                  final newValue = !_isKeepScreenOn;
                                  setSheetState(() {
                                    _isKeepScreenOn = newValue;
                                  });
                                  // 同时更新外部状态
                                  setState(() {
                                    _updateWakelock();
                                  });
                                  // 保存设置到本地存储
                                  _saveKeepScreenOnSetting(newValue);
                                },
                              ),
                              // ListTile(
                              //   leading: const Icon(Icons.color_lens),
                              //   title: const Text('主题颜色'),
                              //   onTap: () {
                              //     // 处理主题设置
                              //     Navigator.pop(context);
                              //   },
                              // ),
                            ],
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建普通视图
  // 暂时未使用，保留供后续开发
  // ignore: unused_element
  Widget _buildNormalView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 科目和项目信息
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              children: [
                Text(
                  widget.subject.name,
                  style: AppTextStyles.headline2,
                ),
                const SizedBox(height: 8),
                Text(
                  widget.project.name,
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),

          // 计时器 - 使用固定key确保状态保持
          widget.isCountdown
              ? _buildCountdownTimer()
              : _buildForwardTimer(),

          // 状态文本 - 使用AnimatedBuilder监听状态变化
          AnimatedBuilder(
            animation: _timerState,
            builder: (context, _) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 24.0),
                child: Text(
                  _timerState.isCompleted
                      ? '专注完成！'
                      : _timerState.isPaused
                          ? '已暂停'
                          : '专注中...',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: _timerState.isCompleted
                        ? AppColors.success
                        : _timerState.isPaused
                            ? AppColors.warning
                            : AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // // 构建简洁视图
  // Widget _buildSimpleView() {
  //   return Center(
  //     child: widget.isCountdown
  //         ? _buildCountdownTimer()
  //         : _buildForwardTimer(),
  //   );
  // }

  // 构建简洁视图
  // 暂时未使用，保留供后续开发
  // ignore: unused_element
  Widget _buildSimpleView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 科目和项目信息
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              children: [
                Text(
                  '',
                  style: AppTextStyles.headline2,
                ),
                const SizedBox(height: 8),
                Text(
                  '',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),

          // 计时器 - 使用固定key确保状态保持
          widget.isCountdown
              ? _buildCountdownTimer()
              : _buildForwardTimer(),

          // 状态文本 - 使用AnimatedBuilder监听状态变化
          AnimatedBuilder(
            animation: _timerState,
            builder: (context, _) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 24.0),
                child: Text(
                  _timerState.isCompleted
                      ? ''
                      : _timerState.isPaused
                          ? ''
                          : '',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: _timerState.isCompleted
                        ? AppColors.success
                        : _timerState.isPaused
                            ? AppColors.warning
                            : AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }


  // 构建倒计时计时器
  Widget _buildCountdownTimer() {
    // 计算总时间（秒）
    final duration = (widget.countdownMinutes! * 60).toInt();

    return CircularCountDownTimer(
      key: _countdownTimerKey,
      duration: duration,
      initialDuration: 0,
      controller: _timerState.controller,
      width: 250,
      height: 250,
      ringColor: Colors.white,
      fillColor: const Color(0x4D4CAF50), // AppColors.primary with 30% opacity
      backgroundColor: const Color(0x1AFFFFFF), // Colors.white with 10% opacity
      strokeWidth: 15.0,
      strokeCap: StrokeCap.round,
      textStyle: const TextStyle(
        fontSize: 40.0,
        color: AppColors.text,
        fontWeight: FontWeight.bold,
      ),
      textFormat: CountdownTextFormat.HH_MM_SS,
      isReverse: true,
      isReverseAnimation: true,
      isTimerTextShown: true,
      autoStart: !widget.isRecovering, // 恢复模式下不自动开始
      onStart: () {
        // 使用addPostFrameCallback延迟状态更新调用
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _timerState.setTimerState(
            isRunning: true,
            isPaused: false,
            isCompleted: false,
          );
          // 倒计时开始时更新屏幕常亮状态
          _updateWakelock();
        });
      },
      onComplete: () async {
        _timerState.setTimerState(
          isRunning: false,
          isPaused: false,
          isCompleted: true,
        );

        // 倒计时完成时更新屏幕常亮状态（禁用）
        _updateWakelock();

        // 移除用户可见的完成提示，保留debugPrint日志
        debugPrint('专注时间已完成！');

        // 检查是否在专注开始后60秒内完成
        if (!_isOverThreeMinutes) {
          // 60秒内完成，记录日志但不显示用户提示
          debugPrint('专注时间少于60秒，不计入统计');
          return;
        }

        // 倒计时完成时保存记录，使用安全的去重逻辑
        await _saveFocusRecord();

        debugPrint('倒计时完成，专注记录已保存');

        // 只有在超过60秒且项目是自定义进度模式时，才显示进度调整界面
        if (widget.project.isTrackingEnabled &&
            widget.project.trackingMode == ProgressTrackingMode.custom) {
          setState(() {
            _showCompletionUI = true;
          });
        }
      },
    );
  }

  // 构建正计时计时器
  Widget _buildForwardTimer() {
    return AnimatedBuilder(
      animation: _timerState,
      builder: (context, _) {
        return Container(
          key: _forwardTimerKey,
          width: 250,
          height: 250,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: const Color(0x1AFFFFFF), // Colors.white with 10% opacity
            border: Border.all(
              color: const Color(0x4D4CAF50), // AppColors.primary with 30% opacity
              width: 15,
            ),
          ),
          child: Center(
            child: Text(
              _timerState.elapsedTimeText,
              style: const TextStyle(
                fontSize: 40.0,
                color: AppColors.text,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }

  // 构建底部控制按钮
  Widget _buildBottomControls() {
    return AnimatedBuilder(
      animation: _timerState,
      builder: (context, _) {
        return Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 主要按钮行
              Row(
                mainAxisAlignment: _showCompletionUI
                    ? MainAxisAlignment.center // 在进度调整状态下居中显示
                    : MainAxisAlignment.spaceEvenly, // 在正常状态下平均分布
                children: [
              // 暂停/继续按钮 - 只在非进度调整状态下显示
              if (!_showCompletionUI)
                ElevatedButton.icon(
                  onPressed: _timerState.isCompleted || !_isOverThreeMinutes
                      ? null // 60秒内或已完成时禁用
                      : () {
                          if (widget.isCountdown) {
                            if (_timerState.isPaused) {
                              _timerState.controller.resume();
                              _timerState.setTimerState(
                                isRunning: true,
                                isPaused: false,
                                isCompleted: false,
                              );
                              // 倒计时恢复时更新屏幕常亮状态
                              _updateWakelock();
                            } else {
                              _timerState.controller.pause();
                              _timerState.setTimerState(
                                isRunning: true,
                                isPaused: true,
                                isCompleted: false,
                              );
                              // 倒计时暂停时更新屏幕常亮状态
                              _updateWakelock();
                            }
                          } else {
                            if (_timerState.isPaused) {
                              _timerState.resumeForwardTimer();
                            } else {
                              _timerState.pauseForwardTimer();
                            }
                          }

                          // 更新屏幕常亮状态
                          _updateWakelock();
                        },
                  icon: Icon(_timerState.isPaused ? Icons.play_arrow : Icons.pause),
                  label: Text(_timerState.isPaused ? '继续' : '暂停'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                    disabledBackgroundColor: const Color(0x4D9E9E9E), // Colors.grey with 30% opacity
                    disabledForegroundColor: const Color(0x80FFFFFF), // Colors.white with 50% opacity
                  ),
                ),

              // 结束/退出按钮
              if (_showCompletionUI)
                // 进度调整状态下的退出按钮
                ElevatedButton.icon(
                  onPressed: _endFocus,
                  icon: const Icon(Icons.exit_to_app),
                  label: const Text('退出'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                )
              else if (!_isOverThreeMinutes)
                // 60秒内显示取消按钮
                ElevatedButton.icon(
                  onPressed: _endFocus, // 直接调用结束方法，会直接返回
                  icon: const Icon(Icons.close),
                  label: const Text('取消'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade400, // 使用灰色表示取消
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                )
              else if (!widget.isCountdown)
                // 正计时模式下的长按结束按钮
                LongPressButton(
                  onLongPressComplete: _endFocus,
                  label: '结束',
                  icon: Icons.stop,
                  backgroundColor: Colors.red.shade400,
                )
              else if (_timerState.isCompleted)
                // 倒计时完成后的退出按钮
                ElevatedButton.icon(
                  onPressed: () async {
                    // 倒计时完成后统一调用_endFocus处理，避免重复保存
                    await _endFocus();
                  },
                  icon: const Icon(Icons.exit_to_app),
                  label: const Text('退出'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                )
              else
                // 倒计时进行中的结束按钮
                ElevatedButton.icon(
                  onPressed: _endFocus,
                  icon: const Icon(Icons.stop),
                  label: const Text('结束'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade400,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
                ],
              ),

              // 移除倒计时显示
            ],
          ),
        );
      },
    );
  }

  // 构建进度调整组件
  Widget _buildProgressAdjustmentWidget() {
    // 如果项目没有开启进度追踪或者不是自定义模式，返回空容器
    if (!widget.project.isTrackingEnabled ||
        widget.project.trackingMode != ProgressTrackingMode.custom) {
      return Container();
    }

    // 获取自定义单位和目标值
    final String unit = widget.project.customUnit ?? '单位';
    final int targetValue = widget.project.targetValue ?? 100;
    final int currentValue = widget.project.currentCustomValue ?? 0;

    // 计算专注时长
    final int durationSeconds = widget.isCountdown
        ? (widget.countdownMinutes != null ? (widget.countdownMinutes! * 60).toInt() : 0)
        : _timerState.elapsedSeconds;
    final double focusHours = durationSeconds / 3600.0;

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
        // 专注时间圆环
        Stack(
          alignment: Alignment.center,
          children: [
            // 圆环背景
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: const Color(0x1AFFFFFF), // Colors.white with 10% opacity
                border: Border.all(
                  color: const Color(0x4D4CAF50), // AppColors.primary with 30% opacity
                  width: 15,
                ),
              ),
            ),
            // 专注时间文本
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '已专注',
                  style: AppTextStyles.bodyLarge.copyWith(color: AppColors.textSecondary),
                ),
                const SizedBox(height: 8),
                Text(
                  focusHours.toStringAsFixed(2),
                  style: const TextStyle(
                    fontSize: 40.0,
                    color: AppColors.text,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text('小时', style: AppTextStyles.bodyMedium),
              ],
            ),
          ],
        ),

        // 进度调整卡片
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.only(top: 16, left: 16, right: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: const Color(0x0D000000), // Colors.black with 5% opacity
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('调整项目进度', style: AppTextStyles.headline3),
                  Text(_hasUnsavedProgress
                       ? '暂存: $_tempProgressValue / $targetValue $unit'
                       : '当前: $currentValue / $targetValue $unit',
                       style: AppTextStyles.bodyMedium.copyWith(
                         color: _hasUnsavedProgress ? AppColors.primary : AppColors.textSecondary)),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _progressController,
                      keyboardType: const TextInputType.numberWithOptions(decimal: false),
                      enabled: _isInputEnabled, // 根据状态控制是否可输入
                      decoration: InputDecoration(
                        labelText: _isInputEnabled ? '增加数量' : '已增加数量',
                        border: const OutlineInputBorder(),
                        suffixText: unit,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: () async {
                      // 如果已经更新过，点击重置按钮
                      if (!_isInputEnabled) {
                        setState(() {
                          _isInputEnabled = true;
                          _progressAdjusted = false;
                          _hasTriedToExit = false;
                          _hasUnsavedProgress = false;
                          _tempProgressValue = null;
                          _progressController.text = '1'; // 重置为默认值
                        });
                        return;
                      }

                      // 获取输入值
                      final int inputValue = int.tryParse(_progressController.text) ?? 0;
                      if (inputValue <= 0) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('请输入有效的数量')),
                        );
                        return;
                      }

                      // 暂存进度值，不立即保存到数据库
                      final int newCustomValue = currentValue + inputValue;

                      // 更新状态
                      setState(() {
                        _tempProgressValue = newCustomValue;
                        _hasUnsavedProgress = true;
                        _progressAdjusted = true;
                        _isInputEnabled = false; // 禁用输入
                      });

                      // 移除用户可见的暂存提示，保留debugPrint日志
                      debugPrint('进度已暂存为 $newCustomValue $unit，点击退出保存');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    child: Text(_isInputEnabled ? '更新' : '重置'),
                  ),
                ],
              ),
            ],
          ),
        ),
        ],
      ),
    );
  }
}

