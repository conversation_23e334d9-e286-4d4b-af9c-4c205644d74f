import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../core/providers/subscription_provider.dart';
import '../../../shared/theme/constants.dart';

/// 订阅状态显示卡片
/// 在解锁Pro界面顶部显示用户当前的订阅情况
class SubscriptionStatusCard extends ConsumerWidget {
  const SubscriptionStatusCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subscriptionStatus = ref.watch(appleSubscriptionStatusProvider);

    return subscriptionStatus.when(
      data: (isPremium) {
        if (isPremium) {
          return _buildPremiumStatusCard(context, ref);
        } else {
          return const SizedBox.shrink(); // 未订阅时不显示
        }
      },
      loading: () => _buildLoadingCard(),
      error: (error, _) => const SizedBox.shrink(),
    );
  }

  /// 构建高级用户状态卡片
  Widget _buildPremiumStatusCard(BuildContext context, WidgetRef ref) {
    return FutureBuilder<Map<String, dynamic>?>(
      future: ref.read(appleSubscriptionServiceProvider).getSubscriptionDetails(),
      builder: (context, snapshot) {
        final subscriptionDetails = snapshot.data;

        return Container(
          margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.15),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部状态行
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppColors.primary.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.verified,
                          color: AppColors.primary,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Pro 用户',
                          style: TextStyle(
                            color: AppColors.primary,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.workspace_premium,
                    color: AppColors.primary.withValues(alpha: 0.6),
                    size: 18,
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // 订阅信息
              if (subscriptionDetails != null) ...[
                _buildSubscriptionInfo(subscriptionDetails),
              ] else ...[
                _buildDefaultInfo(),
              ],
            ],
          ),
        );
      },
    );
  }

  /// 构建订阅详细信息
  Widget _buildSubscriptionInfo(Map<String, dynamic> details) {
    final productId = details['productId'] as String?;
    final expiryDate = details['expiryDate'] as String?;
    final daysRemaining = details['daysRemaining'] as int? ?? 0;

    // 解析订阅类型和简短描述
    final subscriptionInfo = _getSubscriptionInfo(productId);

    // 解析到期日期
    DateTime? expiryDateParsed;
    if (expiryDate != null) {
      expiryDateParsed = DateTime.tryParse(expiryDate);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 订阅类型和描述
        Text(
          subscriptionInfo['name']!,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),

        const SizedBox(height: 4),

        Text(
          subscriptionInfo['description']!,
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[600],
          ),
        ),

        const SizedBox(height: 16),

        // 到期时间和剩余天数
        Row(
          children: [
            // 到期时间
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.event,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 6),
                      Text(
                        '到期时间',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    expiryDateParsed != null
                        ? DateFormat('yyyy年MM月dd日').format(expiryDateParsed)
                        : '未知',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // 剩余天数状态
            if (daysRemaining > 0) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: _getStatusColor(daysRemaining).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: _getStatusColor(daysRemaining).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getStatusIcon(daysRemaining),
                      color: _getStatusColor(daysRemaining),
                      size: 14,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      '$daysRemaining天',
                      style: TextStyle(
                        color: _getStatusColor(daysRemaining),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  /// 构建默认信息（当无法获取详细信息时）
  Widget _buildDefaultInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'LimeFocus Pro',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),

        const SizedBox(height: 8),

        Text(
          '感谢您的支持！您已解锁所有高级功能',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// 获取订阅信息（名称和描述）
  Map<String, String> _getSubscriptionInfo(String? productId) {
    switch (productId) {
      case 'LemiVip001':
        return {
          'name': 'Pro 月度订阅',
          'description': '每月自动续费，随时可取消',
        };
      case 'LimeVip_quarter':
        return {
          'name': 'Pro 季度订阅',
          'description': '每季度自动续费，享受优惠价格',
        };
      case 'LimeVip_yearly':
        return {
          'name': 'Pro 年度订阅',
          'description': '每年自动续费，享受最大优惠',
        };
      case 'LimeVip_AYear':
        return {
          'name': '一年备考包',
          'description': '一次性购买，无需续费',
        };
      default:
        return {
          'name': 'LimeFocus Pro',
          'description': '高级功能已解锁',
        };
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(int daysRemaining) {
    if (daysRemaining > 30) {
      return Colors.green;
    } else if (daysRemaining > 7) {
      return Colors.blue;
    } else if (daysRemaining > 3) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }

  /// 获取状态图标
  IconData _getStatusIcon(int daysRemaining) {
    if (daysRemaining > 30) {
      return Icons.check_circle;
    } else if (daysRemaining > 7) {
      return Icons.info;
    } else if (daysRemaining > 3) {
      return Icons.warning;
    } else {
      return Icons.error;
    }
  }

  /// 构建加载状态卡片
  Widget _buildLoadingCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 12),
          Text(
            '正在检查订阅状态...',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }


}
