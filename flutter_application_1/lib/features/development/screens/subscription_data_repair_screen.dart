import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/utils/storage_utils.dart';
import '../../../core/providers/subscription_provider.dart';
import '../../../shared/theme/constants.dart';

/// 订阅数据修复工具
class SubscriptionDataRepairScreen extends ConsumerStatefulWidget {
  const SubscriptionDataRepairScreen({super.key});

  @override
  ConsumerState<SubscriptionDataRepairScreen> createState() => _SubscriptionDataRepairScreenState();
}

class _SubscriptionDataRepairScreenState extends ConsumerState<SubscriptionDataRepairScreen> {
  bool _isRepairing = false;
  String? _repairResult;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('订阅数据修复工具'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 16),
            _buildRepairActions(),
            const SizedBox(height: 16),
            if (_repairResult != null) _buildResultCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  '订阅数据修复工具',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '此工具用于修复损坏的订阅数据，解决以下问题：',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            const Text(
              '• JSON格式错误导致的解析失败',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const Text(
              '• 时间戳格式不正确',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const Text(
              '• 数据结构不完整',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRepairActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '修复操作',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRepairing ? null : _repairSubscriptionData,
                icon: _isRepairing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.build),
                label: Text(_isRepairing ? '修复中...' : '修复订阅数据'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRepairing ? null : _clearAllSubscriptionData,
                icon: const Icon(Icons.clear_all),
                label: const Text('清除所有订阅数据'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRepairing ? null : _checkDataIntegrity,
                icon: const Icon(Icons.check_circle),
                label: const Text('检查数据完整性'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.assignment_turned_in, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  '修复结果',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _repairResult!,
                style: const TextStyle(
                  fontSize: 12,
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _repairSubscriptionData() async {
    setState(() {
      _isRepairing = true;
      _repairResult = null;
    });

    try {
      final result = StringBuffer();
      result.writeln('开始修复订阅数据...');

      // 1. 修复损坏的数据
      await StorageUtils.repairCorruptedSubscriptionData();
      result.writeln('✅ 损坏数据修复完成');

      // 2. 检查当前数据状态
      final subscriptionData = await StorageUtils.getSubscriptionData();
      if (subscriptionData != null) {
        result.writeln('✅ 找到有效的订阅数据');
        result.writeln('产品ID: ${subscriptionData['productId']}');
        result.writeln('购买时间: ${subscriptionData['purchaseDate']}');
        result.writeln('到期时间: ${subscriptionData['expiryDate']}');
      } else {
        result.writeln('ℹ️ 没有找到订阅数据');
      }

      // 3. 刷新Provider状态
      ref.invalidate(appleSubscriptionStatusProvider);
      result.writeln('✅ Provider状态已刷新');

      setState(() {
        _repairResult = result.toString();
      });

      _showSuccess('订阅数据修复完成');
    } catch (e) {
      setState(() {
        _repairResult = '❌ 修复失败: $e';
      });
      _showError('修复失败: $e');
    } finally {
      setState(() {
        _isRepairing = false;
      });
    }
  }

  Future<void> _clearAllSubscriptionData() async {
    final confirmed = await _showConfirmDialog(
      '确认清除',
      '这将清除所有订阅数据，包括购买记录。确定要继续吗？',
    );

    if (!confirmed) return;

    setState(() {
      _isRepairing = true;
      _repairResult = null;
    });

    try {
      final result = StringBuffer();
      result.writeln('开始清除订阅数据...');

      // 清除所有订阅相关数据
      await StorageUtils.clearSubscriptionData();
      result.writeln('✅ 本地订阅数据已清除');

      // 清除Apple服务缓存
      final service = ref.read(appleSubscriptionServiceProvider);
      service.clearCache();
      result.writeln('✅ 服务缓存已清除');

      // 刷新Provider状态
      ref.invalidate(appleSubscriptionStatusProvider);
      ref.invalidate(appleSubscriptionProductsProvider);
      result.writeln('✅ Provider状态已刷新');

      setState(() {
        _repairResult = result.toString();
      });

      _showSuccess('所有订阅数据已清除');
    } catch (e) {
      setState(() {
        _repairResult = '❌ 清除失败: $e';
      });
      _showError('清除失败: $e');
    } finally {
      setState(() {
        _isRepairing = false;
      });
    }
  }

  Future<void> _checkDataIntegrity() async {
    setState(() {
      _isRepairing = true;
      _repairResult = null;
    });

    try {
      final result = StringBuffer();
      result.writeln('开始检查数据完整性...');

      // 检查订阅数据
      final subscriptionData = await StorageUtils.getSubscriptionData();
      if (subscriptionData != null) {
        result.writeln('✅ 订阅数据存在');

        // 检查必要字段
        final requiredFields = ['productId', 'purchaseDate', 'expiryDate', 'purchaseID'];
        for (final field in requiredFields) {
          if (subscriptionData.containsKey(field) && subscriptionData[field] != null) {
            result.writeln('✅ $field: ${subscriptionData[field]}');
          } else {
            result.writeln('❌ 缺少字段: $field');
          }
        }

        // 检查日期格式
        try {
          final purchaseDate = DateTime.parse(subscriptionData['purchaseDate'] ?? '');
          final expiryDate = DateTime.parse(subscriptionData['expiryDate'] ?? '');
          result.writeln('✅ 日期格式正确');
          result.writeln('购买时间: $purchaseDate');
          result.writeln('到期时间: $expiryDate');

          final isActive = expiryDate.isAfter(DateTime.now());
          result.writeln('订阅状态: ${isActive ? "有效" : "已过期"}');
        } catch (e) {
          result.writeln('❌ 日期格式错误: $e');
        }
      } else {
        result.writeln('ℹ️ 没有找到订阅数据');
      }

      // 检查Apple服务状态
      final service = ref.read(appleSubscriptionServiceProvider);
      result.writeln('Apple服务初始化状态: ${service.isInitialized}');
      result.writeln('可用产品数量: ${service.availableProductsCount}');

      setState(() {
        _repairResult = result.toString();
      });

      _showSuccess('数据完整性检查完成');
    } catch (e) {
      setState(() {
        _repairResult = '❌ 检查失败: $e';
      });
      _showError('检查失败: $e');
    } finally {
      setState(() {
        _isRepairing = false;
      });
    }
  }

  Future<bool> _showConfirmDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('确定'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
