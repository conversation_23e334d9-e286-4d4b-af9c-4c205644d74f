import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io';

import '../../../core/providers/subscription_provider.dart';
import '../../../shared/theme/constants.dart';

/// Apple功能调试页面
/// 用于排查Apple订阅和登录相关问题
class AppleDebugScreen extends ConsumerStatefulWidget {
  const AppleDebugScreen({super.key});

  @override
  ConsumerState<AppleDebugScreen> createState() => _AppleDebugScreenState();
}

class _AppleDebugScreenState extends ConsumerState<AppleDebugScreen> {
  String _debugInfo = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDebugInfo();
  }

  Future<void> _loadDebugInfo() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final service = ref.read(appleSubscriptionServiceProvider);

      final info = StringBuffer();
      info.writeln('=== Apple功能调试信息 ===\n');

      // 平台信息
      info.writeln('📱 平台信息:');
      info.writeln('- 当前平台: ${Platform.operatingSystem}');
      info.writeln('- 是否iOS: ${Platform.isIOS}');
      info.writeln('- 调试模式: $kDebugMode');
      info.writeln('- 发布模式: $kReleaseMode');
      info.writeln('');

      // 订阅服务状态
      info.writeln('🔧 订阅服务状态:');
      info.writeln('- 服务已初始化: ${service.isInitialized}');
      info.writeln('- 可用产品数量: ${service.availableProductsCount}');
      info.writeln('');

      // 尝试获取产品信息
      info.writeln('📦 产品信息:');
      try {
        final products = await service.getSubscriptionProducts().timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            info.writeln('- ⏰ 产品加载超时');
            return [];
          },
        );
        if (products.isNotEmpty) {
          info.writeln('- 成功加载 ${products.length} 个产品');
          for (final product in products) {
            info.writeln('  • ${product['title']}: ${product['price']}');
          }
        } else {
          info.writeln('- ⚠️ 未加载到任何产品');
        }
      } catch (e) {
        info.writeln('- ❌ 产品加载失败: $e');
      }
      info.writeln('');

      // 订阅状态
      info.writeln('⭐ 订阅状态:');
      try {
        final isPremium = await service.isPremiumUser().timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            info.writeln('- ⏰ 状态检查超时');
            return false;
          },
        );
        info.writeln('- 当前是高级用户: $isPremium');
      } catch (e) {
        info.writeln('- ❌ 状态检查失败: $e');
      }
      info.writeln('');

      // 网络连接测试
      info.writeln('🌐 网络连接:');
      try {
        final result = await InternetAddress.lookup('apple.com');
        if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
          info.writeln('- ✅ 网络连接正常');
        } else {
          info.writeln('- ❌ 网络连接异常');
        }
      } catch (e) {
        info.writeln('- ❌ 网络测试失败: $e');
      }
      info.writeln('');

      // 配置检查
      info.writeln('⚙️ 配置检查:');
      info.writeln('- Bundle ID: com.arborflame.limefocus');
      info.writeln('- 产品ID配置:');
      info.writeln('  • LemiVip001 (月度订阅)');
      info.writeln('  • LimeVip_quarter (季度订阅)');
      info.writeln('  • LimeVip_yearly (年度订阅)');
      info.writeln('');

      // 建议
      info.writeln('💡 排查建议:');
      if (!Platform.isIOS) {
        info.writeln('- ❌ 当前不是iOS平台，Apple功能无法使用');
      } else if (!service.isInitialized) {
        info.writeln('- ❌ 订阅服务未初始化，检查网络和配置');
      } else if (service.availableProductsCount == 0) {
        info.writeln('- ❌ 未加载到产品，检查App Store Connect配置');
        info.writeln('- 确保产品已在App Store Connect中创建');
        info.writeln('- 等待24小时让配置生效');
        info.writeln('- 检查Bundle ID是否匹配');
      } else {
        info.writeln('- ✅ 基础配置正常，可以进行功能测试');
      }

      if (mounted) {
        setState(() {
          _debugInfo = info.toString();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _debugInfo = '调试信息加载失败: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Apple功能调试'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDebugInfo,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 调试信息显示
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '调试信息',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              _debugInfo,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 测试按钮
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '功能测试',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),

                          // 重新初始化服务
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _reinitializeService,
                              icon: const Icon(Icons.restart_alt),
                              label: const Text('重新初始化订阅服务'),
                            ),
                          ),

                          const SizedBox(height: 8),

                          // 强制刷新产品
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _forceRefreshProducts,
                              icon: const Icon(Icons.shopping_cart),
                              label: const Text('强制刷新产品信息'),
                            ),
                          ),

                          const SizedBox(height: 8),

                          // 设置测试订阅状态
                          Row(
                            children: [
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: () => _setTestSubscription(true),
                                  icon: const Icon(Icons.verified_user),
                                  label: const Text('设为Pro用户'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: ElevatedButton.icon(
                                  onPressed: () => _setTestSubscription(false),
                                  icon: const Icon(Icons.person),
                                  label: const Text('设为普通用户'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.grey,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 8),

                          // 测试不同订阅类型
                          const Text(
                            '测试订阅类型',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),

                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              _buildSubscriptionTypeButton('月度', 'LemiVip001'),
                              _buildSubscriptionTypeButton('季度', 'LimeVip_quarter'),
                              _buildSubscriptionTypeButton('年度', 'LimeVip_yearly'),
                              _buildSubscriptionTypeButton('备考包', 'LimeVip_AYear'),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // 清除缓存
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _clearCache,
                              icon: const Icon(Icons.clear_all),
                              label: const Text('清除订阅缓存'),
                            ),
                          ),

                          const SizedBox(height: 8),

                          // 测试网络连接
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton.icon(
                              onPressed: _testNetworkConnection,
                              icon: const Icon(Icons.wifi),
                              label: const Text('测试网络连接'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 常见问题
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '常见问题解决方案',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),

                          _buildSolutionItem(
                            '产品加载失败',
                            [
                              '1. 检查App Store Connect中的产品配置',
                              '2. 确认Bundle ID匹配：com.arborflame.limefocus',
                              '3. 等待24小时让配置生效',
                              '4. 检查网络连接',
                            ],
                          ),

                          const SizedBox(height: 12),

                          _buildSolutionItem(
                            'Apple登录无反应',
                            [
                              '1. 确保在真机上测试（模拟器不支持）',
                              '2. 检查entitlements文件配置',
                              '3. 在设备设置中退出Apple ID',
                              '4. 重启设备和应用',
                            ],
                          ),

                          const SizedBox(height: 12),

                          _buildSolutionItem(
                            '购买流程失败',
                            [
                              '1. 使用沙盒测试账号',
                              '2. 检查设备的购买限制设置',
                              '3. 确保产品在App Store Connect中已配置',
                              '4. 检查开发者账号状态',
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSolutionItem(String title, List<String> solutions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 4),
        ...solutions.map((solution) => Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 2),
          child: Text(
            solution,
            style: const TextStyle(fontSize: 12),
          ),
        )),
      ],
    );
  }

  Future<void> _reinitializeService() async {
    if (!mounted) return;

    try {
      final service = ref.read(appleSubscriptionServiceProvider);
      await service.initialize();
      _showSuccess('订阅服务重新初始化完成');
      await _loadDebugInfo();
    } catch (e) {
      _showError('重新初始化失败: $e');
    }
  }

  Future<void> _forceRefreshProducts() async {
    if (!mounted) return;

    try {
      final service = ref.read(appleSubscriptionServiceProvider);
      service.clearCache();
      await service.getSubscriptionProducts();
      _showSuccess('产品信息刷新完成');
      await _loadDebugInfo();
    } catch (e) {
      _showError('刷新产品失败: $e');
    }
  }

  Future<void> _clearCache() async {
    if (!mounted) return;

    try {
      final service = ref.read(appleSubscriptionServiceProvider);
      service.clearCache();
      _showSuccess('缓存清除完成');
      await _loadDebugInfo();
    } catch (e) {
      _showError('清除缓存失败: $e');
    }
  }

  Future<void> _setTestSubscription(bool isPremium) async {
    if (!mounted) return;

    try {
      final service = ref.read(appleSubscriptionServiceProvider);
      await service.setTestPremiumStatus(isPremium);

      // 刷新订阅状态
      ref.invalidate(appleSubscriptionStatusProvider);

      _showSuccess(isPremium ? '已设置为Pro用户' : '已设置为普通用户');
      await _loadDebugInfo();
    } catch (e) {
      _showError('设置测试状态失败: $e');
    }
  }

  Widget _buildSubscriptionTypeButton(String label, String productId) {
    return SizedBox(
      height: 32,
      child: ElevatedButton(
        onPressed: () => _setTestSubscriptionType(productId),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue.shade50,
          foregroundColor: Colors.blue.shade700,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          textStyle: const TextStyle(fontSize: 12),
        ),
        child: Text(label),
      ),
    );
  }

  Future<void> _setTestSubscriptionType(String productId) async {
    if (!mounted) return;

    try {
      // 首先设置为Pro用户
      final service = ref.read(appleSubscriptionServiceProvider);
      await service.setTestPremiumStatus(true);

      // 然后设置特定的订阅类型（这里需要扩展服务来支持设置特定类型）
      // 目前先刷新状态
      ref.invalidate(appleSubscriptionStatusProvider);

      _showSuccess('已设置为 $productId 订阅类型');
      await _loadDebugInfo();
    } catch (e) {
      _showError('设置订阅类型失败: $e');
    }
  }

  Future<void> _testNetworkConnection() async {
    if (!mounted) return;

    try {
      final result = await InternetAddress.lookup('apple.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        _showSuccess('网络连接正常');
      } else {
        _showError('网络连接异常');
      }
    } catch (e) {
      _showError('网络测试失败: $e');
    }
  }

  void _showSuccess(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showError(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
