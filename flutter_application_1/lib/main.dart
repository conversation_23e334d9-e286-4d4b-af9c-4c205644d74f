import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'features/profile/screens/profile_screen.dart';
import 'features/home/<USER>/home_screen.dart';
import 'features/task/screens/task_goal_screen_riverpod.dart';
import 'features/schedule/screens/task_schedule_screen.dart';
import 'features/focus/screens/focus_module_screen.dart';
import 'shared/widgets/bottom_navigation.dart';
import 'shared/theme/app_theme.dart';
import 'core/services/notification_service.dart';

import 'core/routes/app_routes.dart';
import 'core/providers/hive_service_provider.dart';
import 'core/providers/focus_session_recovery_provider.dart';
import 'core/models/focus_session.dart';
import 'features/focus/screens/focus_screen.dart';
import 'features/debug/quick_fix_verification.dart';

// 应用的入口点，运行 MyApp 组件。
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 设置状态栏样式为深色内容（黑色文字）
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent, // 透明状态栏
      statusBarIconBrightness: Brightness.dark, // 深色图标
      statusBarBrightness: Brightness.light, // iOS状态栏亮色背景
    ),
  );

  // 初始化通知服务
  try {
    debugPrint('主函数中初始化通知服务');
    await NotificationService().init();
    debugPrint('通知服务初始化成功');
  } catch (e) {
    debugPrint('通知服务初始化失败: $e');
  }

  // 运行修复验证（仅在调试模式下）
  runStartupVerification();

  // 使用ProviderScope包装应用，启用Riverpod状态管理
  // Hive的初始化将在MyApp中通过Provider处理
  runApp(const ProviderScope(child: MyApp()));
}

// MyApp 是一个无状态组件，它是整个应用的根组件。
// 它定义了应用的标题和主题，并设置了主屏幕。
class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  // 构建方法，返回一个 MaterialApp 组件。
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      // 应用的标题，显示在任务栏或应用切换器中。
      title: 'LimeFocus',
      // 隐藏右上角的debug标识，用于App Store截图
      debugShowCheckedModeBanner: false,
      // 应用的主题，使用 AppTheme 类中定义的 lightTheme。
      theme: AppTheme.lightTheme.copyWith(
        appBarTheme: AppBarTheme(
          systemOverlayStyle: SystemUiOverlayStyle.dark.copyWith(
            statusBarColor: Colors.transparent, // 透明状态栏
          ),
        ),
      ),

      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('zh', 'CN'), // 中文简体
        Locale('en', 'US'), // 英文
      ],
      locale: const Locale('zh', 'CN'), // 强制使用中文

      // 路由配置
      initialRoute: '/', // 直接使用主页面作为入口
      routes: AppRoutes.routes,
      onGenerateRoute: AppRoutes.onGenerateRoute,
      onUnknownRoute: AppRoutes.onUnknownRoute,
    );
  }


}

// 定义底部导航栏索引的状态管理Provider
final navigationIndexProvider = StateProvider<int>((ref) => 0);

// MainScreen 是应用的主屏幕，包含专注会话恢复功能。
// 它包含一个底部导航栏和多个子屏幕，通过底部导航栏切换显示。
class MainScreen extends ConsumerStatefulWidget {
  const MainScreen({super.key});

  @override
  ConsumerState<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends ConsumerState<MainScreen> {

  // 子屏幕列表，包含五个主要页面
  static final List<Widget> _screens = [
    const HomeScreen(),
    const FocusModuleScreen(),
    const TaskGoalScreenRiverpod(),
    const TaskScheduleScreen(),
    const ProfileScreen(),
  ];

  // 阶段4：处理专注会话恢复
  void _handleFocusSessionRecovery(BuildContext context, FocusRecoveryData recoveryData) {
    if (recoveryData.state == FocusRecoveryState.found && recoveryData.session != null) {
      // 发现需要恢复的专注会话，自动跳转到专注页面
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _navigateToFocusScreen(context, recoveryData.session!);
      });
    }
  }

  // 阶段4：导航到专注页面恢复会话
  void _navigateToFocusScreen(BuildContext context, FocusSession session) async {
    try {
      debugPrint('阶段4：准备恢复专注会话到专注页面');
      debugPrint('  - 会话ID: ${session.id}');
      debugPrint('  - 科目ID: ${session.subjectId}');
      debugPrint('  - 项目ID: ${session.projectId}');

      // 获取科目和项目数据
      final hiveService = ref.read(hiveServiceProvider);
      await hiveService.initHive();

      final subject = hiveService.subjectRepository.getSubjectById(session.subjectId);
      final project = hiveService.subjectRepository.getProjectById(session.projectId);

      if (subject == null || project == null) {
        debugPrint('阶段4：无法找到科目或项目数据，清理无效会话');
        debugPrint('  - 科目存在: ${subject != null}');
        debugPrint('  - 项目存在: ${project != null}');

        // 清理无效会话
        await ref.read(focusSessionRecoveryProvider.notifier).clearSession();
        return;
      }

      debugPrint('阶段4：成功获取科目和项目数据');
      debugPrint('  - 科目: ${subject.name}');
      debugPrint('  - 项目: ${project.name}');

      // 检查mounted状态，避免async gap问题
      if (!mounted) return;

      // 导航到专注页面
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => FocusScreen(
            isCountdown: session.isCountdown,
            countdownMinutes: session.isCountdown && session.countdownDurationSeconds != null
                ? (session.countdownDurationSeconds! / 60).toDouble()
                : null,
            subject: subject,
            project: project,
            isRecovering: true, // 标记为恢复模式
          ),
        ),
      );

      // 标记会话已恢复
      ref.read(focusSessionRecoveryProvider.notifier).markAsRecovered();

      debugPrint('阶段4：专注会话恢复导航完成');

    } catch (e) {
      debugPrint('阶段4：恢复专注会话失败: $e');
      // 清理出错的会话
      await ref.read(focusSessionRecoveryProvider.notifier).clearSession();
    }
  }

  // 构建方法，返回一个 Scaffold 组件。
  @override
  Widget build(BuildContext context) {
    // 监听当前选中的子屏幕索引
    final currentIndex = ref.watch(navigationIndexProvider);

    // 在后台初始化Hive，但不阻塞UI显示
    ref.watch(hiveInitializationProvider);

    // 阶段4：监听专注会话恢复状态
    ref.listen<FocusRecoveryData>(focusSessionRecoveryProvider, (previous, next) {
      _handleFocusSessionRecovery(context, next);
    });

    return Scaffold(
      // 显示当前选中的子屏幕。
      body: _screens[currentIndex],
      // 底部导航栏，用于切换子屏幕。
      bottomNavigationBar: BottomNavigation(
        // 当前选中的子屏幕索引。
        currentIndex: currentIndex,
        // 点击事件处理函数，更新当前选中的子屏幕索引。
        onTap: (index) => ref.read(navigationIndexProvider.notifier).state = index,
      ),
    );
  }
}