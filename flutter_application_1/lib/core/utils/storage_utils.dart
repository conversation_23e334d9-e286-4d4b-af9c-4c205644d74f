import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';

// 令牌存储类型
enum TokenStorageType {
  session, // 会话存储，应用关闭后失效
  persistent // 持久存储，应用关闭后仍然有效
}

class StorageUtils {
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userInfoKey = 'user_info';
  static const String _tokenStorageTypeKey = 'token_storage_type';
  static const String _subscriptionDataKey = 'subscription_data';
  static const String _testPremiumStatusKey = 'test_premium_status';

  // 保存令牌
  static Future<void> saveToken(String token, {bool rememberMe = false}) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);

    // 保存令牌存储类型
    final storageType = rememberMe ? TokenStorageType.persistent.name : TokenStorageType.session.name;
    await prefs.setString(_tokenStorageTypeKey, storageType);

    debugPrint('令牌已保存，存储类型: ${rememberMe ? '持久' : '会话'}');
  }

  // 获取令牌
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  // 保存刷新令牌
  static Future<void> saveRefreshToken(String refreshToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_refreshTokenKey, refreshToken);
  }

  // 获取刷新令牌
  static Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  // 保存用户信息
  static Future<void> saveUserInfo(String userJson) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userInfoKey, userJson);
  }

  // 获取用户信息
  static Future<String?> getUserInfo() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userInfoKey);
  }

  // 获取令牌存储类型
  static Future<TokenStorageType> getTokenStorageType() async {
    final prefs = await SharedPreferences.getInstance();
    final storageTypeStr = prefs.getString(_tokenStorageTypeKey);

    // 默认为会话存储
    if (storageTypeStr == null) {
      return TokenStorageType.session;
    }

    return storageTypeStr == TokenStorageType.persistent.name
        ? TokenStorageType.persistent
        : TokenStorageType.session;
  }

  // 检查令牌是否应该有效
  // 如果是会话存储，则在应用重启后应该清除令牌
  static Future<bool> shouldTokenBeValid() async {
    final storageType = await getTokenStorageType();

    // 如果是持久存储，则令牌应该有效
    if (storageType == TokenStorageType.persistent) {
      return true;
    }

    // 如果是会话存储，则检查应用是否刚刚启动
    // 这里简单返回true，实际应用中可能需要更复杂的逻辑
    return true;
  }

  // 清除认证数据
  static Future<void> clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_userInfoKey);
    await prefs.remove(_tokenStorageTypeKey);

    debugPrint('认证数据已清除');
  }

  // 保存订阅数据
  static Future<void> saveSubscriptionData(Map<String, dynamic> subscriptionData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = json.encode(subscriptionData);
      await prefs.setString(_subscriptionDataKey, jsonString);
      debugPrint('订阅数据已保存: $jsonString');
    } catch (e) {
      debugPrint('保存订阅数据失败: $e');
      rethrow;
    }
  }

  // 获取订阅数据
  static Future<Map<String, dynamic>?> getSubscriptionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_subscriptionDataKey);
      debugPrint('从存储获取的订阅数据字符串: $jsonString');

      if (jsonString != null && jsonString.isNotEmpty) {
        try {
          final Map<String, dynamic> data = json.decode(jsonString);
          debugPrint('解析的订阅数据: $data');
          return data;
        } catch (jsonError) {
          debugPrint('JSON解析失败: $jsonError');
          debugPrint('数据格式可能不正确，清除损坏的数据');

          // 清除损坏的数据
          await prefs.remove(_subscriptionDataKey);
          debugPrint('已清除损坏的订阅数据');
          return null;
        }
      }

      debugPrint('没有找到订阅数据');
      return null;
    } catch (e) {
      debugPrint('获取订阅数据失败: $e');
      return null;
    }
  }

  // 设置测试付费状态
  static Future<void> setTestPremiumStatus(bool isPremium) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_testPremiumStatusKey, isPremium);
    debugPrint('测试付费状态已设置: $isPremium');
  }

  // 获取测试付费状态
  static Future<bool?> getTestPremiumStatus() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_testPremiumStatusKey);
  }

  // 清除订阅数据
  static Future<void> clearSubscriptionData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_subscriptionDataKey);
    await prefs.remove(_testPremiumStatusKey);
    debugPrint('订阅数据已清除');
  }

  // 修复损坏的订阅数据
  static Future<void> repairCorruptedSubscriptionData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_subscriptionDataKey);

      if (jsonString != null && jsonString.isNotEmpty) {
        try {
          // 尝试解析JSON
          json.decode(jsonString);
          debugPrint('订阅数据格式正常');
        } catch (e) {
          debugPrint('发现损坏的订阅数据，正在清除: $e');
          await prefs.remove(_subscriptionDataKey);
          debugPrint('损坏的订阅数据已清除');
        }
      }
    } catch (e) {
      debugPrint('修复订阅数据失败: $e');
    }
  }

  // 清除所有数据
  // 用于测试环境重置
  static Future<void> clearAllData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();

    debugPrint('所有数据已清除');
  }
}
