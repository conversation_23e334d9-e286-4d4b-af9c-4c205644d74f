import 'package:flutter/foundation.dart';
import '../models/focus_record.dart';
import '../services/enhanced_hive_service.dart';

/// 专注数据修复工具
/// 用于检测和修复重复的专注记录
class FocusDataRepair {
  final EnhancedHiveService _hiveService = EnhancedHiveService();

  /// 检测重复的专注记录
  Future<List<List<FocusRecord>>> detectDuplicateRecords() async {
    try {
      await _hiveService.initHive();
      final allRecords = _hiveService.focusRecordRepository.getAllFocusRecords();

      // 按时间排序
      allRecords.sort((a, b) => a.startTime.compareTo(b.startTime));

      final duplicateGroups = <List<FocusRecord>>[];

      for (int i = 0; i < allRecords.length - 1; i++) {
        final current = allRecords[i];
        final duplicates = <FocusRecord>[current];

        // 查找可能的重复记录
        for (int j = i + 1; j < allRecords.length; j++) {
          final next = allRecords[j];

          // 判断是否为重复记录的条件：
          // 1. 同一项目和科目
          // 2. 开始时间相近（5分钟内）
          // 3. 时长相似或相同
          if (isPotentialDuplicate(current, next)) {
            duplicates.add(next);
          } else {
            // 时间差太大，跳出内层循环
            if (next.startTime.difference(current.startTime).inMinutes > 5) {
              break;
            }
          }
        }

        if (duplicates.length > 1) {
          duplicateGroups.add(duplicates);
          debugPrint('发现重复记录组: ${duplicates.length}条记录');
          for (final record in duplicates) {
            debugPrint('  - ID: ${record.id}, 时长: ${record.durationSeconds}秒, 开始时间: ${record.startTime}');
          }
        }
      }

      return duplicateGroups;
    } catch (e) {
      debugPrint('检测重复记录失败: $e');
      return [];
    }
  }

  /// 判断两条记录是否可能是重复的
  bool isPotentialDuplicate(FocusRecord record1, FocusRecord record2) {
    // 必须是同一项目和科目
    if (record1.projectId != record2.projectId ||
        record1.subjectId != record2.subjectId) {
      return false;
    }

    // 开始时间相近（5分钟内）
    final timeDiff = (record1.startTime.difference(record2.startTime)).abs();
    if (timeDiff.inMinutes > 5) {
      return false;
    }

    // 时长相似（差异不超过10%或60秒）
    final durationDiff = (record1.durationSeconds - record2.durationSeconds).abs();
    final maxDuration = record1.durationSeconds > record2.durationSeconds
        ? record1.durationSeconds
        : record2.durationSeconds;

    if (durationDiff <= 60 || durationDiff / maxDuration <= 0.1) {
      return true;
    }

    return false;
  }

  /// 修复重复记录（保留最准确的记录，删除其他）
  Future<int> repairDuplicateRecords() async {
    try {
      final duplicateGroups = await detectDuplicateRecords();
      int repairedCount = 0;

      for (final group in duplicateGroups) {
        if (group.length <= 1) continue;

        // 选择最准确的记录（通常是时长最长的，因为可能包含了完整的专注时间）
        final bestRecord = selectBestRecord(group);

        // 删除其他记录
        for (final record in group) {
          if (record.id != bestRecord.id) {
            await _hiveService.focusRecordRepository.deleteFocusRecord(record.id);
            repairedCount++;
            debugPrint('删除重复记录: ${record.id}');
          }
        }

        debugPrint('保留最佳记录: ${bestRecord.id}, 时长: ${bestRecord.durationSeconds}秒');
      }

      debugPrint('数据修复完成，删除了 $repairedCount 条重复记录');
      return repairedCount;
    } catch (e) {
      debugPrint('修复重复记录失败: $e');
      return 0;
    }
  }

  /// 选择最佳记录
  FocusRecord selectBestRecord(List<FocusRecord> records) {
    if (records.length == 1) return records.first;

    // 优先选择状态为完成的记录
    final completedRecords = records.where((r) => r.status == FocusRecordStatus.completed).toList();
    if (completedRecords.isNotEmpty) {
      // 在完成的记录中选择时长最长的
      completedRecords.sort((a, b) => b.durationSeconds.compareTo(a.durationSeconds));
      return completedRecords.first;
    }

    // 如果没有完成的记录，选择时长最长的
    records.sort((a, b) => b.durationSeconds.compareTo(a.durationSeconds));
    return records.first;
  }

  /// 生成数据修复报告
  Future<String> generateRepairReport() async {
    try {
      final duplicateGroups = await detectDuplicateRecords();
      final buffer = StringBuffer();

      buffer.writeln('=== 专注数据修复报告 ===');
      buffer.writeln('检测时间: ${DateTime.now()}');
      buffer.writeln('发现重复记录组: ${duplicateGroups.length}');

      int totalDuplicates = 0;
      for (int i = 0; i < duplicateGroups.length; i++) {
        final group = duplicateGroups[i];
        totalDuplicates += group.length - 1; // 减1是因为要保留一条

        buffer.writeln('\n--- 重复组 ${i + 1} ---');
        buffer.writeln('记录数量: ${group.length}');

        for (final record in group) {
          buffer.writeln('ID: ${record.id}');
          buffer.writeln('  时长: ${record.durationSeconds}秒 (${(record.durationSeconds / 3600).toStringAsFixed(2)}小时)');
          buffer.writeln('  开始时间: ${record.startTime}');
          buffer.writeln('  状态: ${record.status}');
          buffer.writeln('  项目: ${record.projectId}');
        }

        final bestRecord = selectBestRecord(group);
        buffer.writeln('推荐保留: ${bestRecord.id} (时长: ${bestRecord.durationSeconds}秒)');
      }

      buffer.writeln('\n=== 总结 ===');
      buffer.writeln('需要删除的重复记录: $totalDuplicates 条');
      buffer.writeln('预计节省的存储空间: ${totalDuplicates * 0.5}KB');

      return buffer.toString();
    } catch (e) {
      return '生成修复报告失败: $e';
    }
  }

  /// 验证数据完整性
  Future<bool> validateDataIntegrity() async {
    try {
      await _hiveService.initHive();
      final allRecords = _hiveService.focusRecordRepository.getAllFocusRecords();

      bool hasIssues = false;

      for (final record in allRecords) {
        // 检查时长是否合理（不能为负数，不能超过6小时）
        if (record.durationSeconds < 0 || record.durationSeconds > 6 * 3600) {
          debugPrint('发现异常时长记录: ${record.id}, 时长: ${record.durationSeconds}秒');
          hasIssues = true;
        }

        // 检查开始时间是否晚于结束时间
        if (record.startTime.isAfter(record.endTime)) {
          debugPrint('发现时间异常记录: ${record.id}, 开始时间晚于结束时间');
          hasIssues = true;
        }

        // 检查计算的时长是否与记录的时长匹配
        final calculatedDuration = record.endTime.difference(record.startTime).inSeconds;
        if ((calculatedDuration - record.durationSeconds).abs() > 60) {
          debugPrint('发现时长计算异常记录: ${record.id}, 计算时长: ${calculatedDuration}秒, 记录时长: ${record.durationSeconds}秒');
          hasIssues = true;
        }
      }

      if (!hasIssues) {
        debugPrint('数据完整性验证通过');
      }

      return !hasIssues;
    } catch (e) {
      debugPrint('数据完整性验证失败: $e');
      return false;
    }
  }
}
