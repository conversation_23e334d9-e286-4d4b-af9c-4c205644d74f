import 'dart:convert';

/// 专注会话模型
/// 用于持久化专注状态，支持后台计时和状态恢复
class FocusSession {
  /// 会话唯一标识
  final String id;

  /// 科目ID
  final String subjectId;

  /// 项目ID
  final String projectId;

  /// 专注开始时间
  final DateTime startTime;

  /// 是否为倒计时模式
  final bool isCountdown;

  /// 倒计时总时长（秒），仅倒计时模式有效
  final int? countdownDurationSeconds;

  /// 是否暂停
  final bool isPaused;

  /// 暂停开始时间，仅暂停状态有效
  final DateTime? pausedTime;

  /// 累计暂停时长（秒）
  final int pausedDurationSeconds;

  /// 会话创建时间
  final DateTime createdAt;

  /// 最后更新时间
  final DateTime updatedAt;

  const FocusSession({
    required this.id,
    required this.subjectId,
    required this.projectId,
    required this.startTime,
    required this.isCountdown,
    this.countdownDurationSeconds,
    required this.isPaused,
    this.pausedTime,
    required this.pausedDurationSeconds,
    required this.createdAt,
    required this.updatedAt,
  });

  /// 计算当前已经过的时间（秒）
  int get currentElapsedSeconds {
    final now = DateTime.now();

    if (isPaused) {
      // 暂停状态：计算暂停前的有效专注时间
      // 总经过时间 - 累计暂停时间 - 当前暂停时间
      final totalElapsed = (pausedTime ?? now).difference(startTime).inSeconds;
      return totalElapsed - pausedDurationSeconds;
    } else {
      // 运行状态：计算总经过时间 - 累计暂停时间
      final totalElapsed = now.difference(startTime).inSeconds;
      return totalElapsed - pausedDurationSeconds;
    }
  }

  /// 计算倒计时剩余时间（秒），仅倒计时模式有效
  int get remainingSeconds {
    if (!isCountdown || countdownDurationSeconds == null) {
      return 0;
    }

    final elapsed = currentElapsedSeconds;
    final remaining = countdownDurationSeconds! - elapsed;
    return remaining > 0 ? remaining : 0;
  }

  /// 检查倒计时是否已完成
  bool get isCountdownCompleted {
    return isCountdown && remainingSeconds <= 0;
  }

  /// 检查会话是否有效（未超过最大时长限制）
  bool get isValid {
    const maxDurationHours = 6;
    const maxDurationSeconds = maxDurationHours * 3600;

    // 检查会话是否超过最大时长
    if (currentElapsedSeconds > maxDurationSeconds) {
      return false;
    }

    // 检查会话是否过于陈旧（超过24小时）
    final now = DateTime.now();
    if (now.difference(createdAt).inHours > 24) {
      return false;
    }

    return true;
  }

  /// 创建暂停状态的会话副本
  FocusSession pause() {
    if (isPaused) return this; // 已经暂停，直接返回

    final now = DateTime.now();
    return copyWith(
      isPaused: true,
      pausedTime: now,
      updatedAt: now,
    );
  }

  /// 创建恢复状态的会话副本
  FocusSession resume() {
    if (!isPaused) return this; // 未暂停，直接返回

    final now = DateTime.now();
    int newPausedDuration = pausedDurationSeconds;

    // 如果有暂停开始时间，计算本次暂停时长
    if (pausedTime != null) {
      final pauseDuration = now.difference(pausedTime!).inSeconds;
      newPausedDuration += pauseDuration;
    }

    return copyWith(
      isPaused: false,
      pausedTime: null,
      pausedDurationSeconds: newPausedDuration,
      updatedAt: now,
    );
  }

  /// 创建副本
  FocusSession copyWith({
    String? id,
    String? subjectId,
    String? projectId,
    DateTime? startTime,
    bool? isCountdown,
    int? countdownDurationSeconds,
    bool? isPaused,
    DateTime? pausedTime,
    int? pausedDurationSeconds,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FocusSession(
      id: id ?? this.id,
      subjectId: subjectId ?? this.subjectId,
      projectId: projectId ?? this.projectId,
      startTime: startTime ?? this.startTime,
      isCountdown: isCountdown ?? this.isCountdown,
      countdownDurationSeconds: countdownDurationSeconds ?? this.countdownDurationSeconds,
      isPaused: isPaused ?? this.isPaused,
      pausedTime: pausedTime ?? this.pausedTime,
      pausedDurationSeconds: pausedDurationSeconds ?? this.pausedDurationSeconds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'subjectId': subjectId,
      'projectId': projectId,
      'startTime': startTime.toIso8601String(),
      'isCountdown': isCountdown,
      'countdownDurationSeconds': countdownDurationSeconds,
      'isPaused': isPaused,
      'pausedTime': pausedTime?.toIso8601String(),
      'pausedDurationSeconds': pausedDurationSeconds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 从JSON创建
  factory FocusSession.fromJson(Map<String, dynamic> json) {
    return FocusSession(
      id: json['id'] as String,
      subjectId: json['subjectId'] as String,
      projectId: json['projectId'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      isCountdown: json['isCountdown'] as bool,
      countdownDurationSeconds: json['countdownDurationSeconds'] as int?,
      isPaused: json['isPaused'] as bool,
      pausedTime: json['pausedTime'] != null
          ? DateTime.parse(json['pausedTime'] as String)
          : null,
      pausedDurationSeconds: json['pausedDurationSeconds'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// 从JSON字符串创建
  factory FocusSession.fromJsonString(String jsonString) {
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    return FocusSession.fromJson(json);
  }

  /// 转换为JSON字符串
  String toJsonString() {
    return jsonEncode(toJson());
  }

  @override
  String toString() {
    return 'FocusSession(id: $id, subjectId: $subjectId, projectId: $projectId, '
           'isCountdown: $isCountdown, isPaused: $isPaused, '
           'elapsed: ${currentElapsedSeconds}s)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FocusSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
