# Apple Connect 上传指南 - LimeFocus v1.0.4

## 📋 上传前检查清单

### 应用信息验证
- [x] Bundle ID: com.arborflame.limefocus
- [x] 版本号: 1.0.4
- [x] Build号: 10
- [x] 应用名称: LimeFocus
- [x] 开发者账号: 已配置

### 订阅产品配置验证
- [ ] LemiVip001 (月度订阅) - 已配置并审核通过
- [ ] LimeVip_quarter (季度订阅) - 已配置并审核通过
- [ ] LimeVip_yearly (年度订阅) - 已配置并审核通过
- [ ] LimeVip_AYear (一年备考包) - 已配置并审核通过

### 代码签名和证书
- [ ] 开发者证书有效
- [ ] 分发证书有效
- [ ] Provisioning Profile正确
- [ ] 自动签名配置正确

## 🛠️ 构建步骤

### 1. Xcode构建配置

```bash
# 打开Xcode项目
open ios/Runner.xcworkspace
```

**Xcode中的配置检查**：
1. **Target设置**：
   - Product Name: LimeFocus
   - Bundle Identifier: com.arborflame.limefocus
   - Version: 1.0.4
   - Build: 10

2. **Signing & Capabilities**：
   - Team: 选择正确的开发者团队
   - Signing Certificate: Apple Distribution
   - Provisioning Profile: 自动或手动选择正确的Profile

3. **Build Settings**：
   - Code Signing Identity: Apple Distribution
   - Development Team: 正确的Team ID

### 2. 构建Release版本

**方法一：使用Flutter命令**
```bash
# 清理并构建
flutter clean
flutter pub get
flutter build ios --release
```

**方法二：使用Xcode**
1. 在Xcode中选择 "Any iOS Device (arm64)"
2. Product → Archive
3. 等待构建完成

### 3. 上传到App Store Connect

**使用Xcode Organizer**：
1. 构建完成后，Xcode会自动打开Organizer
2. 选择刚刚构建的Archive
3. 点击 "Distribute App"
4. 选择 "App Store Connect"
5. 选择 "Upload"
6. 确认配置并上传

**使用Application Loader（备选）**：
```bash
# 如果有.ipa文件，可以使用命令行上传
xcrun altool --upload-app -f LimeFocus.ipa -u your-apple-id -p your-app-password
```

## 📝 App Store Connect配置

### 1. 应用信息更新

**版本信息**：
- 版本号: 1.0.4
- 构建版本: 选择刚上传的Build 10

**版本说明**：
```
🔧 订阅功能全面优化

本次更新重点修复了订阅相关的关键问题：

✅ 修复订阅状态显示错误
• 解决年度会员显示为月度到期的问题
• 准确显示所有订阅类型的到期时间和剩余天数

✅ 优化购买体验
• 修复购买过程中的黑屏问题
• 提升购买流程的稳定性和响应速度
• 优化错误处理和用户提示

✅ 增强应用稳定性
• 修复内存管理相关问题
• 优化数据处理和存储机制
• 提升整体应用性能

感谢您的支持和反馈！
```

### 2. 审核信息

**审核备注**：
```
本次更新主要修复订阅功能的关键问题：

1. 修复了订阅状态显示错误的问题
2. 优化了购买流程的稳定性
3. 增强了应用的整体性能

测试账号信息：
- 沙盒测试账号已配置
- 所有订阅产品均可正常购买和测试

技术改进：
- 修复了数据处理和存储机制
- 优化了内存管理
- 增强了错误处理机制

请重点测试订阅购买流程和状态显示功能。
```

**联系信息**：
- 邮箱: <EMAIL>
- 电话: （如有）

### 3. 隐私清单检查

确认以下隐私清单项目：
- [ ] 数据收集说明准确
- [ ] 第三方SDK隐私清单已更新
- [ ] 用户数据处理说明清晰

### 4. 订阅产品验证

在App Store Connect中验证：
- [ ] 所有订阅产品状态为"Ready to Submit"
- [ ] 价格和描述正确
- [ ] 订阅组配置正确

## 🧪 提交前最终测试

### 沙盒环境测试
1. **订阅购买测试**：
   - [ ] 月度订阅购买成功
   - [ ] 季度订阅购买成功
   - [ ] 年度订阅购买成功
   - [ ] 一年备考包购买成功

2. **状态显示测试**：
   - [ ] 购买后状态立即更新
   - [ ] 到期时间显示正确
   - [ ] 剩余天数计算准确

3. **应用稳定性测试**：
   - [ ] 应用启动正常
   - [ ] 页面切换流畅
   - [ ] 无崩溃和内存泄漏

### TestFlight内测（可选）
如果需要进一步测试：
1. 上传后选择TestFlight分发
2. 邀请内测用户
3. 收集反馈并修复问题

## 📊 提交后监控

### 审核状态跟踪
- **Waiting for Review**: 等待审核
- **In Review**: 审核中
- **Pending Developer Release**: 等待开发者发布
- **Ready for Sale**: 审核通过，可以发布

### 关键指标监控
1. **审核时间**: 通常1-7天
2. **拒绝原因**: 如有拒绝，及时查看原因
3. **用户反馈**: 发布后关注用户评价

## 🚨 常见问题和解决方案

### 构建问题
- **签名错误**: 检查证书和Provisioning Profile
- **依赖问题**: 运行`flutter clean && flutter pub get`
- **Xcode版本**: 确保使用最新版本Xcode

### 上传问题
- **网络超时**: 使用稳定网络，必要时使用VPN
- **文件大小**: 确保应用大小在限制范围内
- **元数据错误**: 检查App Store Connect中的应用信息

### 审核问题
- **订阅功能**: 确保订阅产品配置正确
- **隐私政策**: 确保隐私说明完整准确
- **功能描述**: 确保版本说明与实际功能一致

## 📞 支持联系

如遇到问题，可以联系：
- **Apple开发者支持**: 通过开发者账号提交技术支持请求
- **项目邮箱**: <EMAIL>

---

**准备发布 LimeFocus v1.0.4！** 🚀
