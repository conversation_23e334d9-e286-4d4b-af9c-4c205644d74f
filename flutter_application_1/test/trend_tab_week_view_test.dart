import 'package:flutter_test/flutter_test.dart';
import 'package:limefocus/core/models/focus_record.dart';
import 'package:limefocus/features/data/utils/time_period_utils.dart';

void main() {
  group('TrendTab周视图数据过滤测试', () {
    late List<FocusRecord> testRecords;
    late DateTime testDate;

    setUp(() {
      // 设置测试日期为2024年1月15日（周一）
      testDate = DateTime(2024, 1, 15);
      
      // 创建测试数据
      testRecords = [
        // 本周记录（1月15日-1月21日）
        FocusRecord(
          id: 'record1',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: DateTime(2024, 1, 15, 9, 0), // 周一
          endTime: DateTime(2024, 1, 15, 10, 0),
          durationSeconds: 3600,
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        FocusRecord(
          id: 'record2',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: DateTime(2024, 1, 17, 14, 0), // 周三
          endTime: DateTime(2024, 1, 17, 15, 0),
          durationSeconds: 3600,
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        FocusRecord(
          id: 'record3',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: DateTime(2024, 1, 21, 16, 0), // 周日
          endTime: DateTime(2024, 1, 21, 17, 0),
          durationSeconds: 3600,
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        // 上周记录（1月8日-1月14日）
        FocusRecord(
          id: 'record4',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: DateTime(2024, 1, 10, 10, 0), // 上周三
          endTime: DateTime(2024, 1, 10, 11, 0),
          durationSeconds: 3600,
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        // 下周记录（1月22日-1月28日）
        FocusRecord(
          id: 'record5',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: DateTime(2024, 1, 23, 11, 0), // 下周二
          endTime: DateTime(2024, 1, 23, 12, 0),
          durationSeconds: 3600,
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
      ];
    });

    test('TimePeriodUtils.getWeekStart应该返回正确的周一日期', () {
      // 测试不同日期的周一计算
      expect(TimePeriodUtils.getWeekStart(DateTime(2024, 1, 15)), DateTime(2024, 1, 15)); // 周一
      expect(TimePeriodUtils.getWeekStart(DateTime(2024, 1, 16)), DateTime(2024, 1, 15)); // 周二
      expect(TimePeriodUtils.getWeekStart(DateTime(2024, 1, 17)), DateTime(2024, 1, 15)); // 周三
      expect(TimePeriodUtils.getWeekStart(DateTime(2024, 1, 18)), DateTime(2024, 1, 15)); // 周四
      expect(TimePeriodUtils.getWeekStart(DateTime(2024, 1, 19)), DateTime(2024, 1, 15)); // 周五
      expect(TimePeriodUtils.getWeekStart(DateTime(2024, 1, 20)), DateTime(2024, 1, 15)); // 周六
      expect(TimePeriodUtils.getWeekStart(DateTime(2024, 1, 21)), DateTime(2024, 1, 15)); // 周日
    });

    test('周视图过滤应该返回正确的记录数量', () {
      // 模拟TrendTab的周视图过滤逻辑
      final weekStart = TimePeriodUtils.getWeekStart(testDate);
      final weekEnd = weekStart.add(const Duration(days: 7));

      print('测试周范围: $weekStart - $weekEnd');

      final filtered = testRecords.where((record) {
        final isInRange = record.startTime.isAfter(weekStart.subtract(const Duration(seconds: 1))) && 
                         record.startTime.isBefore(weekEnd);
        
        if (isInRange) {
          print('匹配记录: ${record.id}, 时间: ${record.startTime}');
        }
        
        return isInRange;
      }).toList();

      print('过滤结果: ${filtered.length} 条记录');
      
      // 应该有3条本周记录
      expect(filtered.length, 3);
      expect(filtered.map((r) => r.id).toSet(), {'record1', 'record2', 'record3'});
    });

    test('周视图过滤应该包含周一到周日的所有记录', () {
      // 测试边界情况
      final weekStart = TimePeriodUtils.getWeekStart(testDate);
      final weekEnd = weekStart.add(const Duration(days: 7));

      // 创建边界测试记录
      final boundaryRecords = [
        // 周一00:00:01
        FocusRecord(
          id: 'boundary1',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: DateTime(2024, 1, 15, 0, 0, 1),
          endTime: DateTime(2024, 1, 15, 1, 0, 1),
          durationSeconds: 3600,
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        // 周日23:59:59
        FocusRecord(
          id: 'boundary2',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: DateTime(2024, 1, 21, 23, 59, 59),
          endTime: DateTime(2024, 1, 22, 0, 59, 59),
          durationSeconds: 3600,
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        // 上周日23:59:59（应该被排除）
        FocusRecord(
          id: 'boundary3',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: DateTime(2024, 1, 14, 23, 59, 59),
          endTime: DateTime(2024, 1, 15, 0, 59, 59),
          durationSeconds: 3600,
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        // 下周一00:00:01（应该被排除）
        FocusRecord(
          id: 'boundary4',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: DateTime(2024, 1, 22, 0, 0, 1),
          endTime: DateTime(2024, 1, 22, 1, 0, 1),
          durationSeconds: 3600,
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
      ];

      final filtered = boundaryRecords.where((record) {
        return record.startTime.isAfter(weekStart.subtract(const Duration(seconds: 1))) && 
               record.startTime.isBefore(weekEnd);
      }).toList();

      print('边界测试过滤结果: ${filtered.length} 条记录');
      for (final record in filtered) {
        print('匹配记录: ${record.id}, 时间: ${record.startTime}');
      }

      // 应该包含周一和周日的记录，排除上周日和下周一的记录
      expect(filtered.length, 2);
      expect(filtered.map((r) => r.id).toSet(), {'boundary1', 'boundary2'});
    });

    test('专注次数计算应该正确', () {
      // 测试专注次数计算
      final sessionCount = testRecords.length;
      expect(sessionCount, 5);

      // 测试本周专注次数
      final weekStart = TimePeriodUtils.getWeekStart(testDate);
      final weekEnd = weekStart.add(const Duration(days: 7));

      final thisWeekRecords = testRecords.where((record) {
        return record.startTime.isAfter(weekStart.subtract(const Duration(seconds: 1))) && 
               record.startTime.isBefore(weekEnd);
      }).toList();

      expect(thisWeekRecords.length, 3);
    });
  });
}
