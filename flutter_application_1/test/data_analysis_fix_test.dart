import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:limefocus/features/data/screens/data_detail_screen.dart';
import 'package:limefocus/core/models/focus_record.dart';
import 'package:limefocus/core/models/subject_project.dart';
import 'package:limefocus/shared/theme/app_theme.dart';

void main() {
  group('数据分析页面修复测试', () {
    late List<FocusRecord> mockRecords;
    late List<Subject> mockSubjects;
    late List<Project> mockProjects;

    setUp(() {
      // 创建模拟数据
      mockSubjects = [
        Subject(
          id: 'subject1',
          name: '数学',
          color: 0xFF2196F3, // Colors.blue
        ),
        Subject(
          id: 'subject2',
          name: '英语',
          color: 0xFF4CAF50, // Colors.green
        ),
      ];

      mockProjects = [
        Project(
          id: 'project1',
          name: '高等数学',
          subjectId: 'subject1',
          startDate: DateTime.now().subtract(const Duration(days: 20)),
          endDate: DateTime.now().add(const Duration(days: 30)),
          isTrackingEnabled: true,
          trackingMode: ProgressTrackingMode.focusTime,
          targetValue: 100,
          currentCustomValue: 50,
          progress: 0.5,
        ),
        Project(
          id: 'project2',
          name: '英语阅读',
          subjectId: 'subject2',
          startDate: DateTime.now().subtract(const Duration(days: 15)),
          endDate: DateTime.now().add(const Duration(days: 45)),
          isTrackingEnabled: true,
          trackingMode: ProgressTrackingMode.custom,
          targetValue: 200,
          currentCustomValue: 80,
          progress: 0.4,
        ),
      ];

      // 创建今日和昨日的专注记录
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final yesterday = today.subtract(const Duration(days: 1));

      mockRecords = [
        // 今日记录
        FocusRecord(
          id: 'record1',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: today.add(const Duration(hours: 9)),
          endTime: today.add(const Duration(hours: 10)),
          durationSeconds: 3600, // 1小时
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        FocusRecord(
          id: 'record2',
          subjectId: 'subject2',
          projectId: 'project2',
          startTime: today.add(const Duration(hours: 14)),
          endTime: today.add(const Duration(hours: 15, minutes: 30)),
          durationSeconds: 5400, // 1.5小时
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 90,
        ),
        // 昨日记录
        FocusRecord(
          id: 'record3',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: yesterday.add(const Duration(hours: 10)),
          endTime: yesterday.add(const Duration(hours: 11)),
          durationSeconds: 3600, // 1小时
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        // 本周其他记录
        FocusRecord(
          id: 'record4',
          subjectId: 'subject2',
          projectId: 'project2',
          startTime: today.subtract(const Duration(days: 2)).add(const Duration(hours: 16)),
          endTime: today.subtract(const Duration(days: 2)).add(const Duration(hours: 17)),
          durationSeconds: 3600, // 1小时
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
      ];
    });

    testWidgets('数据分析页面应该正确显示', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: const DataDetailScreen(),
          ),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('数据分析'), findsOneWidget);

      // 验证标签页存在
      expect(find.text('概览'), findsOneWidget);
      expect(find.text('专注'), findsOneWidget);
      expect(find.text('项目'), findsOneWidget);
    });

    testWidgets('标签页切换应该正确工作', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: const DataDetailScreen(),
          ),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 默认应该在概览标签页
      expect(find.text('概览'), findsOneWidget);

      // 点击专注标签页
      await tester.tap(find.text('专注'));
      await tester.pumpAndSettle();

      // 验证专注标签页内容
      // 这里可以添加更多具体的验证

      // 点击项目标签页
      await tester.tap(find.text('项目'));
      await tester.pumpAndSettle();

      // 验证项目标签页内容
      // 这里可以添加更多具体的验证

      // 切换回概览标签页
      await tester.tap(find.text('概览'));
      await tester.pumpAndSettle();

      // 验证概览标签页内容仍然正确显示
      expect(find.text('概览'), findsOneWidget);
    });

    testWidgets('概览标签页应该显示正确的数据', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: const DataDetailScreen(),
          ),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 验证今日概览部分存在
      expect(find.text('今日概览'), findsOneWidget);
      expect(find.text('总体概览'), findsOneWidget);
    });

    group('标签页状态保持测试', () {
      testWidgets('切换标签页后数据应该保持', (WidgetTester tester) async {
        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: const DataDetailScreen(),
            ),
          ),
        );

        // 等待页面加载
        await tester.pumpAndSettle();

        // 在概览页面记录初始状态
        final overviewContent = find.text('今日概览');
        expect(overviewContent, findsOneWidget);

        // 切换到专注标签页
        await tester.tap(find.text('专注'));
        await tester.pumpAndSettle();

        // 切换到项目标签页
        await tester.tap(find.text('项目'));
        await tester.pumpAndSettle();

        // 切换回概览标签页
        await tester.tap(find.text('概览'));
        await tester.pumpAndSettle();

        // 验证概览页面的内容仍然存在
        expect(find.text('今日概览'), findsOneWidget);
        expect(find.text('总体概览'), findsOneWidget);
      });
    });

    group('数据一致性测试', () {
      testWidgets('所有标签页应该使用一致的数据源', (WidgetTester tester) async {
        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: const DataDetailScreen(),
            ),
          ),
        );

        // 等待页面加载
        await tester.pumpAndSettle();

        // 测试概览标签页
        expect(find.text('概览'), findsOneWidget);

        // 切换到专注标签页并验证
        await tester.tap(find.text('专注'));
        await tester.pumpAndSettle();

        // 切换到项目标签页并验证
        await tester.tap(find.text('项目'));
        await tester.pumpAndSettle();

        // 每次切换后都应该能正常显示内容，不应该出现空白或错误
      });
    });
  });
}
