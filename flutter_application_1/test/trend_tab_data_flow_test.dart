import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:limefocus/features/data/widgets/tabs/trend_tab.dart';
import 'package:limefocus/core/models/focus_record.dart';
import 'package:limefocus/shared/theme/app_theme.dart';

void main() {
  group('TrendTab数据流测试', () {
    late List<FocusRecord> testRecords;

    setUp(() {
      // 创建本周的测试数据
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // 计算本周开始日期（周一）
      final weekStart = now.subtract(Duration(days: now.weekday - 1));
      final thisWeekMonday = DateTime(weekStart.year, weekStart.month, weekStart.day);

      // 计算本周其他日期
      final thisWeekTuesday = thisWeekMonday.add(const Duration(days: 1));
      final thisWeekWednesday = thisWeekMonday.add(const Duration(days: 2));

      print('测试数据设置:');
      print('  今天是: $today (星期${now.weekday})');
      print('  本周一: $thisWeekMonday');
      print('  本周二: $thisWeekTuesday');
      print('  本周三: $thisWeekWednesday');

      testRecords = [
        // 今天的记录（2条）
        FocusRecord(
          id: 'today1',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: today.add(const Duration(hours: 9)),
          endTime: today.add(const Duration(hours: 10)),
          durationSeconds: 3600, // 1小时
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        FocusRecord(
          id: 'today2',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: today.add(const Duration(hours: 14)),
          endTime: today.add(const Duration(hours: 15)),
          durationSeconds: 3600, // 1小时
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
        // 本周二的记录（1条）
        FocusRecord(
          id: 'tuesday1',
          subjectId: 'subject1',
          projectId: 'project1',
          startTime: thisWeekTuesday.add(const Duration(hours: 10)),
          endTime: thisWeekTuesday.add(const Duration(hours: 11)),
          durationSeconds: 3600, // 1小时
          isCountdown: true,
          status: FocusRecordStatus.completed,
          plannedDurationMinutes: 60,
        ),
      ];

      print('测试记录时间:');
      for (final record in testRecords) {
        print('  ${record.id}: ${record.startTime}');
      }
    });

    testWidgets('TrendTab应该正确接收和显示数据', (WidgetTester tester) async {
      // 模拟数据传递
      void mockOnRecordsFiltered(List<FocusRecord> records) {
        // 模拟父组件的回调
        print('过滤记录回调: ${records.length} 条记录');
      }

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: TrendTab(
                allRecords: testRecords,
                onRecordsFiltered: mockOnRecordsFiltered,
              ),
            ),
          ),
        ),
      );

      // 等待组件构建完成
      await tester.pumpAndSettle();

      // 验证组件是否正确渲染
      expect(find.byType(TrendTab), findsOneWidget);
    });

    testWidgets('TrendTab应该在数据更新时重新过滤', (WidgetTester tester) async {
      // 初始数据为空
      List<FocusRecord> currentRecords = [];

      void mockOnRecordsFiltered(List<FocusRecord> records) {
        print('过滤记录回调: ${records.length} 条记录');
      }

      // 创建一个StatefulWidget来模拟数据更新
      Widget buildTrendTab(List<FocusRecord> records) {
        return ProviderScope(
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: TrendTab(
                allRecords: records,
                onRecordsFiltered: mockOnRecordsFiltered,
              ),
            ),
          ),
        );
      }

      // 初始构建（空数据）
      await tester.pumpWidget(buildTrendTab(currentRecords));
      await tester.pumpAndSettle();

      // 更新数据
      currentRecords = testRecords;
      await tester.pumpWidget(buildTrendTab(currentRecords));
      await tester.pumpAndSettle();

      // 验证组件仍然正确渲染
      expect(find.byType(TrendTab), findsOneWidget);
    });

    group('数据过滤逻辑测试', () {
      test('周视图过滤应该返回本周记录', () {
        final now = DateTime.now();

        // 计算本周开始日期
        final weekStart = now.subtract(Duration(days: now.weekday - 1));
        final weekEnd = weekStart.add(const Duration(days: 7));

        print('本周范围: $weekStart - $weekEnd');

        // 过滤本周记录
        final thisWeekRecords = testRecords.where((record) {
          return record.startTime.isAfter(weekStart.subtract(const Duration(seconds: 1))) &&
                 record.startTime.isBefore(weekEnd);
        }).toList();

        print('本周记录数: ${thisWeekRecords.length}');

        // 验证过滤结果 - 应该有3条本周记录（今天2条 + 本周二1条）
        expect(thisWeekRecords.length, 3);
      });

      test('今天是星期一的特殊情况', () {
        // 模拟今天是星期一的情况
        final monday = DateTime(2025, 6, 30); // 2025年6月30日是星期一

        // 创建今天（星期一）的记录
        final mondayRecords = [
          FocusRecord(
            id: 'monday_morning',
            subjectId: 'subject1',
            projectId: 'project1',
            startTime: monday.add(const Duration(hours: 9)),
            endTime: monday.add(const Duration(hours: 10)),
            durationSeconds: 3600,
            isCountdown: true,
            status: FocusRecordStatus.completed,
            plannedDurationMinutes: 60,
          ),
          FocusRecord(
            id: 'monday_afternoon',
            subjectId: 'subject1',
            projectId: 'project1',
            startTime: monday.add(const Duration(hours: 14)),
            endTime: monday.add(const Duration(hours: 15)),
            durationSeconds: 3600,
            isCountdown: true,
            status: FocusRecordStatus.completed,
            plannedDurationMinutes: 60,
          ),
        ];

        // 计算今日记录
        final today = DateTime(monday.year, monday.month, monday.day);
        final tomorrow = today.add(const Duration(days: 1));

        final todayRecords = mondayRecords.where((record) {
          return record.startTime.isAfter(today.subtract(const Duration(seconds: 1))) &&
                 record.startTime.isBefore(tomorrow);
        }).toList();

        // 计算本周记录（今天是星期一，所以本周开始就是今天）
        final weekStart = monday.subtract(Duration(days: monday.weekday - 1));
        final weekEnd = weekStart.add(const Duration(days: 7));

        final thisWeekRecords = mondayRecords.where((record) {
          return record.startTime.isAfter(weekStart.subtract(const Duration(seconds: 1))) &&
                 record.startTime.isBefore(weekEnd);
        }).toList();

        print('星期一测试:');
        print('  今日记录数: ${todayRecords.length}');
        print('  本周记录数: ${thisWeekRecords.length}');

        // 验证：今天是星期一，今日记录数应该等于本周记录数
        expect(todayRecords.length, 2);
        expect(thisWeekRecords.length, 2);
        expect(todayRecords.length, thisWeekRecords.length);
      });

      test('日视图过滤应该返回今日记录', () {
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final tomorrow = today.add(const Duration(days: 1));

        print('今日范围: $today - $tomorrow');

        // 过滤今日记录
        final todayRecords = testRecords.where((record) {
          return record.startTime.isAfter(today.subtract(const Duration(seconds: 1))) &&
                 record.startTime.isBefore(tomorrow);
        }).toList();

        print('今日记录数: ${todayRecords.length}');

        // 验证过滤结果
        expect(todayRecords.length, 2); // 应该有2条今日记录
      });
    });

    group('数据计算测试', () {
      test('专注次数计算应该正确', () {
        final sessionCount = testRecords.length;
        expect(sessionCount, 3); // 3条记录
      });

      test('总专注时长计算应该正确', () {
        final totalSeconds = testRecords.fold<int>(
          0, (sum, record) => sum + record.durationSeconds);
        final totalHours = totalSeconds / 3600.0;

        expect(totalHours, 3.0); // 3条记录，每条1小时
      });

      test('平均专注时长计算应该正确', () {
        final totalSeconds = testRecords.fold<int>(
          0, (sum, record) => sum + record.durationSeconds);
        final avgMinutes = (totalSeconds / 60.0) / testRecords.length;

        expect(avgMinutes, 60.0); // 每条记录60分钟
      });
    });
  });
}
